import React, { useState, useEffect } from "react";
import {
  Box,
  Typography,
  Card,
  CardMedia,
  CardContent,
  Button,
  Container,
  Pagination,
  CircularProgress,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
} from "@mui/material";
import { Add, ShoppingCart } from "@mui/icons-material";
import Grid from "@mui/material/Grid";
import { useNavigate } from "react-router-dom";
import bannerImage from "../../assets/banner_flower.png";
import "../styles/FontStyle.scss";
import { useAppDispatch, useAppSelector } from "../../stores/hooks";
import { setFlowerDetail } from "../../stores/reducers/Flower";
import { addCartItem, setCartItems } from "../../stores/reducers/Cart";
import { productsAPI, cartAPI } from "../../services/api";
import { Product } from "../../types";
import { toast } from "react-toastify";

const FlowerShopPage = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { isAuthenticated } = useAppSelector((state) => state.Authentication);

  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [quantity, setQuantity] = useState(1);

  useEffect(() => {
    loadProducts();
  }, []);

  const loadProducts = async () => {
    try {
      setLoading(true);
      console.log('Loading products...');
      const data = await productsAPI.getAll();
      console.log('Products loaded:', data);

      if (Array.isArray(data)) {
        const availableProducts = data.filter((product: Product) => product.isAvailable);
        console.log('Available products:', availableProducts);
        setProducts(availableProducts);
      } else {
        console.error('Products data is not an array:', data);
        setProducts([]);
        toast.error("Dữ liệu sản phẩm không hợp lệ");
      }
    } catch (error: any) {
      console.error('Error loading products:', error);
      toast.error(`Không thể tải danh sách sản phẩm: ${error.message}`);
      setProducts([]);
    } finally {
      setLoading(false);
    }
  };

  const handleClickFlower = (product: Product) => {
    dispatch(setFlowerDetail(product));
    navigate("/flower/detail");
  };

  const handleAddToCart = (product: Product) => {
    if (!isAuthenticated) {
      toast.warning("Vui lòng đăng nhập để thêm sản phẩm vào giỏ hàng");
      navigate("/auth/login");
      return;
    }

    setSelectedProduct(product);
    setQuantity(1);
    setDialogOpen(true);
  };

  const handleConfirmAddToCart = async () => {
    if (!selectedProduct) return;

    try {
      console.log('Adding product to cart:', selectedProduct.name, 'quantity:', quantity);
      await cartAPI.addItem(selectedProduct.productId, quantity);

      // Always reload cart from server to get accurate data
      const cartData = await cartAPI.getCart();
      console.log('Reloaded cart data:', cartData);
      dispatch(setCartItems(cartData));

      toast.success(`Đã thêm ${selectedProduct.name} vào giỏ hàng`);
      setDialogOpen(false);
    } catch (error: any) {
      console.error('Error adding to cart:', error);
      toast.error(`Không thể thêm sản phẩm vào giỏ hàng: ${error.message}`);
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box
        sx={{
          position: "relative",
          height: "500px",
          mb: 4,
          overflow: "hidden",
          borderRadius: 1,
        }}
      >
        <CardMedia
          component="img"
          height="500px"
          image={bannerImage}
          alt="Banner of flowers"
          sx={{
            objectFit: "cover",
            width: "100%",
          }}
        />
        <Typography
          variant="h1"
          component="h1"
          className="workshop-title"
          sx={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
          }}
        >
          Hoa
        </Typography>
      </Box>
      <Container maxWidth="lg" sx={{ mt: 10 }}>
        {loading ? (
          <Box display="flex" justifyContent="center" my={4}>
            <CircularProgress />
          </Box>
        ) : (
          <Grid container spacing={4}>
            {products.map((product) => (
              <Grid size={{ xs: 6, sm: 3 }} key={product.productId}>
                <Card
                  sx={{
                    height: "100%",
                    display: "flex",
                    flexDirection: "column",
                  }}
                >
                  <CardMedia
                    component="img"
                    height="200"
                    image={product.imageUrl}
                    alt={product.name}
                    sx={{ objectFit: "cover" }}
                  />
                  <CardContent
                    sx={{
                      p: 1,
                      pb: 0,
                      backgroundColor: "#9e655c",
                      color: "#fff",
                    }}
                  >
                    <Typography
                      variant="subtitle2"
                      component="div"
                      fontWeight="bold"
                      sx={{ mb: 0 }}
                    >
                      {product.name}
                    </Typography>
                    <Typography
                      variant="body2"
                      color="#fff"
                      sx={{ mb: 1, fontSize: "0.75rem" }}
                    >
                      giá: {product.price.toLocaleString("vi-VN")}đ
                    </Typography>
                  </CardContent>
                  <Box
                    sx={{
                      p: 1,
                      pt: 0,
                      mt: "auto",
                      backgroundColor: "#9e655c",
                      display: "flex",
                      gap: 1,
                    }}
                  >
                    <Button
                      onClick={() => handleClickFlower(product)}
                      variant="contained"
                      size="small"
                      sx={{
                        backgroundColor: "#E8A295",
                        "&:hover": { backgroundColor: "#c6948c" },
                        textTransform: "none",
                        borderRadius: 1,
                        py: 1,
                        flex: 1,
                      }}
                    >
                      Xem chi tiết
                    </Button>
                    <IconButton
                      onClick={() => handleAddToCart(product)}
                      size="small"
                      sx={{
                        backgroundColor: "#E8A295",
                        color: "white",
                        "&:hover": { backgroundColor: "#c6948c" },
                      }}
                    >
                      <ShoppingCart fontSize="small" />
                    </IconButton>
                  </Box>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
        <Box sx={{ display: "flex", justifyContent: "center", mt: 4 }}>
          <Pagination
            count={12}
            variant="outlined"
            shape="rounded"
            defaultPage={8}
            siblingCount={1}
            size="small"
          />
        </Box>
      </Container>

      {/* Add to Cart Dialog */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)}>
        <DialogTitle>Thêm vào giỏ hàng</DialogTitle>
        <DialogContent>
          {selectedProduct && (
            <Box>
              <Typography variant="h6" gutterBottom>
                {selectedProduct.name}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Giá: {selectedProduct.price.toLocaleString("vi-VN")}đ
              </Typography>
              <TextField
                fullWidth
                label="Số lượng"
                type="number"
                value={quantity}
                onChange={(e) => setQuantity(Math.max(1, parseInt(e.target.value) || 1))}
                inputProps={{ min: 1 }}
                margin="normal"
              />
              <Typography variant="body2" sx={{ mt: 2 }}>
                Tổng: {(selectedProduct.price * quantity).toLocaleString("vi-VN")}đ
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>Hủy</Button>
          <Button onClick={handleConfirmAddToCart} variant="contained">
            Thêm vào giỏ hàng
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default FlowerShopPage;
