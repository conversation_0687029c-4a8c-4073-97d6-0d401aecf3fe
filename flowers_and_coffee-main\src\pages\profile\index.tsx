import React, { useState, useEffect } from "react";
import {
  Container,
  Paper,
  Typography,
  TextField,
  Button,
  Grid,
  Box,
  Avatar,
  Divider,
  Alert,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from "@mui/material";
import { Person, Edit, Save, Cancel } from "@mui/icons-material";
import { useAppSelector, useAppDispatch } from "../../stores/hooks";
import { updateProfile } from "../../stores/reducers/Authentication";
import { authAPI } from "../../services/api";
import { toast } from "react-toastify";

const ProfilePage = () => {
  const { user } = useAppSelector((state) => state.Authentication);
  const dispatch = useAppDispatch();

  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const [formData, setFormData] = useState({
    fullName: user?.fullName || "",
    email: user?.email || "",
    phone: user?.phone || "",
    address: user?.address || "",
    gender: user?.gender || "",
    birthDate: user?.birthDate ? user.birthDate.split('T')[0] : "",
  });

  const [passwordData, setPasswordData] = useState({
    oldPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  const [showPasswordForm, setShowPasswordForm] = useState(false);

  useEffect(() => {
    if (user) {
      setFormData({
        fullName: user.fullName || "",
        email: user.email || "",
        phone: user.phone || "",
        address: user.address || "",
        gender: user.gender || "",
        birthDate: user.birthDate ? user.birthDate.split('T')[0] : "",
      });
    }
  }, [user]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePasswordChange = (field: string, value: string) => {
    setPasswordData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = async () => {
    setError("");
    setLoading(true);

    try {
      // Validate password if changing
      if (showPasswordForm) {
        if (!passwordData.oldPassword || !passwordData.newPassword || !passwordData.confirmPassword) {
          setError("Vui lòng điền đầy đủ thông tin mật khẩu");
          setLoading(false);
          return;
        }

        if (passwordData.newPassword !== passwordData.confirmPassword) {
          setError("Mật khẩu mới và xác nhận mật khẩu không khớp");
          setLoading(false);
          return;
        }

        if (passwordData.newPassword.length < 6) {
          setError("Mật khẩu mới phải có ít nhất 6 ký tự");
          setLoading(false);
          return;
        }
      }

      const updateData = {
        fullName: formData.fullName,
        phone: formData.phone,
        address: formData.address,
        gender: formData.gender,
        birthDate: formData.birthDate,
        ...(showPasswordForm && passwordData.newPassword ? {
          oldPassword: passwordData.oldPassword,
          newPassword: passwordData.newPassword,
          confirmPassword: passwordData.confirmPassword
        } : {})
      };

      // Call update API (returns success message)
      await authAPI.updateProfile(updateData);

      // Get fresh user data after update
      const updatedUser = await authAPI.getProfile();
      dispatch(updateProfile(updatedUser));

      setIsEditing(false);
      setShowPasswordForm(false);
      setPasswordData({ oldPassword: "", newPassword: "", confirmPassword: "" });
      toast.success("Cập nhật thông tin thành công!");
    } catch (error: any) {
      const errorMessage = error.message || "Có lỗi xảy ra khi cập nhật thông tin";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    setShowPasswordForm(false);
    setError("");
    setPasswordData({ oldPassword: "", newPassword: "", confirmPassword: "" });

    if (user) {
      setFormData({
        fullName: user.fullName || "",
        email: user.email || "",
        phone: user.phone || "",
        address: user.address || "",
        gender: user.gender || "",
        birthDate: user.birthDate ? user.birthDate.split('T')[0] : "",
      });
    }
  };

  if (!user) {
    return (
      <Container maxWidth="md" sx={{ mt: 4 }}>
        <Alert severity="warning">
          Vui lòng đăng nhập để xem thông tin cá nhân
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
      <Paper elevation={3} sx={{ p: { xs: 2, md: 4 } }}>
        <Box display="flex" alignItems="center" mb={3}>
          <Avatar sx={{ width: 80, height: 80, mr: 3, bgcolor: "primary.main" }}>
            <Person sx={{ fontSize: 40 }} />
          </Avatar>
          <Box>
            <Typography variant="h4" gutterBottom>
              Thông tin cá nhân
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Quản lý thông tin tài khoản của bạn
            </Typography>
          </Box>
        </Box>

        <Divider sx={{ mb: 3 }} />

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Họ và tên"
              value={formData.fullName}
              onChange={(e) => handleInputChange("fullName", e.target.value)}
              disabled={!isEditing}
              variant={isEditing ? "outlined" : "filled"}
              margin="normal"
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Email"
              value={formData.email}
              disabled={true}
              variant="filled"
              helperText="Email không thể thay đổi"
              margin="normal"
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Số điện thoại"
              value={formData.phone}
              onChange={(e) => handleInputChange("phone", e.target.value)}
              disabled={!isEditing}
              variant={isEditing ? "outlined" : "filled"}
              margin="normal"
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControl fullWidth disabled={!isEditing} margin="normal">
              <InputLabel>Giới tính</InputLabel>
              <Select
                value={formData.gender}
                label="Giới tính"
                onChange={(e) => handleInputChange("gender", e.target.value)}
                variant={isEditing ? "outlined" : "filled"}
              >
                <MenuItem value="Male">Nam</MenuItem>
                <MenuItem value="Female">Nữ</MenuItem>
                <MenuItem value="Other">Khác</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Ngày sinh"
              type="date"
              value={formData.birthDate}
              onChange={(e) => handleInputChange("birthDate", e.target.value)}
              disabled={!isEditing}
              variant={isEditing ? "outlined" : "filled"}
              InputLabelProps={{ shrink: true }}
              margin="normal"
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Địa chỉ"
              value={formData.address}
              onChange={(e) => handleInputChange("address", e.target.value)}
              disabled={!isEditing}
              variant={isEditing ? "outlined" : "filled"}
              margin="normal"
              multiline
              rows={3}
            />
          </Grid>
        </Grid>

        <Box mt={4} display="flex" justifyContent="flex-end" gap={2}>
          {isEditing ? (
            <>
              <Button
                variant="outlined"
                color="secondary"
                startIcon={<Cancel />}
                onClick={handleCancel}
                disabled={loading}
              >
                Hủy
              </Button>
              <Button
                variant="contained"
                color="primary"
                startIcon={<Save />}
                onClick={handleSave}
                disabled={loading}
              >
                {loading ? <CircularProgress size={24} /> : "Lưu"}
              </Button>
            </>
          ) : (
            <Button
              variant="contained"
              color="primary"
              startIcon={<Edit />}
              onClick={() => setIsEditing(true)}
            >
              Chỉnh sửa
            </Button>
          )}
        </Box>

        <Divider sx={{ my: 3 }} />

        <Box>
          <Typography variant="h6" gutterBottom>
            Đổi mật khẩu
          </Typography>
          {showPasswordForm ? (
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Mật khẩu cũ"
                  type="password"
                  value={passwordData.oldPassword}
                  onChange={(e) => handlePasswordChange("oldPassword", e.target.value)}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Mật khẩu mới"
                  type="password"
                  value={passwordData.newPassword}
                  onChange={(e) => handlePasswordChange("newPassword", e.target.value)}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Xác nhận mật khẩu mới"
                  type="password"
                  value={passwordData.confirmPassword}
                  onChange={(e) => handlePasswordChange("confirmPassword", e.target.value)}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12}>
                <Box display="flex" justifyContent="flex-end" gap={2}>
                  <Button
                    variant="outlined"
                    color="secondary"
                    onClick={() => setShowPasswordForm(false)}
                    disabled={loading}
                  >
                    Hủy
                  </Button>
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={handleSave}
                    disabled={loading || !passwordData.oldPassword || !passwordData.newPassword || !passwordData.confirmPassword}
                  >
                    {loading ? <CircularProgress size={24} /> : "Lưu mật khẩu"}
                  </Button>
                </Box>
              </Grid>
            </Grid>
          ) : (
            <Button variant="outlined" onClick={() => setShowPasswordForm(true)}>
              Đổi mật khẩu
            </Button>
          )}
        </Box>
      </Paper>
    </Container>
  );
};

export default ProfilePage;
