/****** Script SQL đã được sửa lỗi cho Database [Exe202] ******/
-- Tạo database chỉ khi chưa tồn tại
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'Exe202')
BEGIN
    CREATE DATABASE [Exe202];
END
GO

-- Sử dụng database
USE [Exe202];
GO

-- Thiết lập compatibility level hợp lệ (160 thay vì 170)
-- Bỏ qua nếu không có quyền ALTER DATABASE
BEGIN TRY
    ALTER DATABASE [Exe202] SET COMPATIBILITY_LEVEL = 160;
END TRY
BEGIN CATCH
    PRINT 'Không thể thiết lập compatibility level - bỏ qua';
END CATCH
GO

-- Thiết lập session
SET ANSI_NULLS ON;
GO
SET QUOTED_IDENTIFIER ON;
GO

/****** T<PERSON>o các bảng ******/

-- Bảng Roles
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Roles]') AND type in (N'U'))
BEGIN
CREATE TABLE [dbo].[Roles](
    [RoleId] [int] IDENTITY(1,1) NOT NULL,
    [RoleName] [nvarchar](50) NOT NULL,
PRIMARY KEY CLUSTERED ([RoleId] ASC)
) ON [PRIMARY]
END
GO

-- Bảng Categories
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Categories]') AND type in (N'U'))
BEGIN
CREATE TABLE [dbo].[Categories](
    [CategoryId] [int] IDENTITY(1,1) NOT NULL,
    [Name] [nvarchar](50) NULL,
    [Description] [nvarchar](255) NULL,
PRIMARY KEY CLUSTERED ([CategoryId] ASC)
) ON [PRIMARY]
END
GO

-- Bảng Users
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Users]') AND type in (N'U'))
BEGIN
CREATE TABLE [dbo].[Users](
    [UserId] [int] IDENTITY(1,1) NOT NULL,
    [FullName] [nvarchar](100) NOT NULL,
    [Email] [nvarchar](100) NOT NULL,
    [PasswordHash] [nvarchar](255) NOT NULL,
    [Phone] [nvarchar](20) NULL,
    [Address] [nvarchar](255) NULL,
    [Gender] [nvarchar](10) NULL,
    [BirthDate] [date] NULL,
    [RoleId] [int] NOT NULL,
    [CreatedAt] [datetime] NULL,
    [RefreshToken] [nvarchar](500) NULL,
    [RefreshTokenExpiryTime] [datetime] NULL,
PRIMARY KEY CLUSTERED ([UserId] ASC)
) ON [PRIMARY]
END
GO

-- Bảng Products
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Products]') AND type in (N'U'))
BEGIN
CREATE TABLE [dbo].[Products](
    [ProductId] [int] IDENTITY(1,1) NOT NULL,
    [Name] [nvarchar](100) NULL,
    [Description] [nvarchar](255) NULL,
    [Price] [decimal](10, 2) NULL,
    [ImageUrl] [nvarchar](255) NULL,
    [CategoryId] [int] NULL,
    [IsAvailable] [bit] NULL,
    [CreatedAt] [datetime] NULL,
PRIMARY KEY CLUSTERED ([ProductId] ASC)
) ON [PRIMARY]
END
GO

-- Bảng Orders
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Orders]') AND type in (N'U'))
BEGIN
CREATE TABLE [dbo].[Orders](
    [OrderId] [int] IDENTITY(1,1) NOT NULL,
    [UserId] [int] NULL,
    [OrderDate] [datetime] NULL,
    [TotalAmount] [decimal](10, 2) NULL,
    [DiscountAmount] [decimal](10, 2) NULL,
    [FinalAmount] AS ([TotalAmount]-[DiscountAmount]) PERSISTED,
    [Status] [nvarchar](50) NULL,
    [Note] [nvarchar](255) NULL,
    [DeliveryAddress] [nvarchar](255) NULL,
    [PromotionCode] [nvarchar](50) NULL,
PRIMARY KEY CLUSTERED ([OrderId] ASC)
) ON [PRIMARY]
END
GO

-- Bảng Payments
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Payments]') AND type in (N'U'))
BEGIN
CREATE TABLE [dbo].[Payments](
    [PaymentId] [int] IDENTITY(1,1) NOT NULL,
    [OrderId] [int] NULL,
    [PaymentMethod] [nvarchar](50) NULL,
    [PaidAmount] [decimal](10, 2) NULL,
    [PaymentDate] [datetime] NULL,
    [Status] [nvarchar](50) NULL,
PRIMARY KEY CLUSTERED ([PaymentId] ASC)
) ON [PRIMARY]
END
GO

-- Bảng OrderDetails
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[OrderDetails]') AND type in (N'U'))
BEGIN
CREATE TABLE [dbo].[OrderDetails](
    [OrderDetailId] [int] IDENTITY(1,1) NOT NULL,
    [OrderId] [int] NULL,
    [ProductId] [int] NULL,
    [ProductDetailId] [int] NULL,
    [Quantity] [int] NULL,
    [UnitPrice] [decimal](10, 2) NULL,
PRIMARY KEY CLUSTERED ([OrderDetailId] ASC)
) ON [PRIMARY]
END
GO

-- Bảng CartItems
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[CartItems]') AND type in (N'U'))
BEGIN
CREATE TABLE [dbo].[CartItems](
    [Id] [int] IDENTITY(1,1) NOT NULL,
    [UserId] [int] NOT NULL,
    [ProductId] [int] NOT NULL,
    [Quantity] [int] NOT NULL,
PRIMARY KEY CLUSTERED ([Id] ASC)
) ON [PRIMARY]
END
GO

-- Bảng ProductDetails
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[ProductDetails]') AND type in (N'U'))
BEGIN
CREATE TABLE [dbo].[ProductDetails](
    [ProductDetailId] [int] IDENTITY(1,1) NOT NULL,
    [ProductId] [int] NULL,
    [Size] [nvarchar](20) NULL,
    [Color] [nvarchar](50) NULL,
    [FlowerType] [nvarchar](50) NULL,
    [ExtraPrice] [decimal](10, 2) NULL,
PRIMARY KEY CLUSTERED ([ProductDetailId] ASC)
) ON [PRIMARY]
END
GO

-- Bảng Promotions
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Promotions]') AND type in (N'U'))
BEGIN
CREATE TABLE [dbo].[Promotions](
    [PromotionId] [int] IDENTITY(1,1) NOT NULL,
    [Code] [nvarchar](50) NULL,
    [Description] [nvarchar](255) NULL,
    [DiscountPercent] [int] NULL,
    [MaxDiscount] [decimal](10, 2) NULL,
    [StartDate] [date] NULL,
    [EndDate] [date] NULL,
    [IsActive] [bit] NULL,
PRIMARY KEY CLUSTERED ([PromotionId] ASC)
) ON [PRIMARY]
END
GO

-- Bảng Workshops
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Workshops]') AND type in (N'U'))
BEGIN
CREATE TABLE [dbo].[Workshops](
    [WorkshopId] [int] IDENTITY(1,1) NOT NULL,
    [Title] [nvarchar](100) NULL,
    [Description] [nvarchar](255) NULL,
    [Location] [nvarchar](255) NULL,
    [EventDate] [datetime] NULL,
    [MaxAttendees] [int] NULL,
    [Price] [decimal](10, 2) NULL,
    [ImageUrl] [nvarchar](255) NULL,
    [CreatedAt] [datetime] NULL,
PRIMARY KEY CLUSTERED ([WorkshopId] ASC)
) ON [PRIMARY]
END
GO

-- Bảng WorkshopTickets
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[WorkshopTickets]') AND type in (N'U'))
BEGIN
CREATE TABLE [dbo].[WorkshopTickets](
    [TicketId] [int] IDENTITY(1,1) NOT NULL,
    [WorkshopId] [int] NULL,
    [TicketType] [nvarchar](50) NULL,
    [Price] [decimal](10, 2) NULL,
    [Quantity] [int] NULL,
PRIMARY KEY CLUSTERED ([TicketId] ASC)
) ON [PRIMARY]
END
GO

-- Bảng UserWorkshopTickets
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[UserWorkshopTickets]') AND type in (N'U'))
BEGIN
CREATE TABLE [dbo].[UserWorkshopTickets](
    [UserTicketId] [int] IDENTITY(1,1) NOT NULL,
    [UserId] [int] NULL,
    [TicketId] [int] NULL,
    [Quantity] [int] NULL,
    [PurchaseDate] [datetime] NULL,
PRIMARY KEY CLUSTERED ([UserTicketId] ASC)
) ON [PRIMARY]
END
GO

-- Bảng Feedbacks
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Feedbacks]') AND type in (N'U'))
BEGIN
CREATE TABLE [dbo].[Feedbacks](
    [FeedbackId] [int] IDENTITY(1,1) NOT NULL,
    [UserId] [int] NOT NULL,
    [ProductId] [int] NULL,
    [WorkshopId] [int] NULL,
    [Rating] [int] NULL,
    [Comment] [nvarchar](1000) NULL,
    [CreatedAt] [datetime] NULL,
PRIMARY KEY CLUSTERED ([FeedbackId] ASC)
) ON [PRIMARY]
END
GO

PRINT 'Tất cả bảng đã được tạo thành công!';
GO

/****** Chèn dữ liệu mẫu ******/

-- Dữ liệu cho bảng Roles
SET IDENTITY_INSERT [dbo].[Roles] ON;
INSERT [dbo].[Roles] ([RoleId], [RoleName]) VALUES (1, N'User');
INSERT [dbo].[Roles] ([RoleId], [RoleName]) VALUES (2, N'Staff');
INSERT [dbo].[Roles] ([RoleId], [RoleName]) VALUES (3, N'Guest');
INSERT [dbo].[Roles] ([RoleId], [RoleName]) VALUES (4, N'Admin');
SET IDENTITY_INSERT [dbo].[Roles] OFF;
GO

-- Dữ liệu cho bảng Categories
SET IDENTITY_INSERT [dbo].[Categories] ON;
INSERT [dbo].[Categories] ([CategoryId], [Name], [Description]) VALUES (1, N'Nước Uống', N'Các Loại Nước Mùa Hè');
SET IDENTITY_INSERT [dbo].[Categories] OFF;
GO

-- Dữ liệu cho bảng Products
SET IDENTITY_INSERT [dbo].[Products] ON;
INSERT [dbo].[Products] ([ProductId], [Name], [Description], [Price], [ImageUrl], [CategoryId], [IsAvailable], [CreatedAt])
VALUES (1, N'Nước Chanh Leo', N'Một Loại Nước chua chua, ngọt ngọt, phù hợp với mùa hè nóng nực', CAST(10000.00 AS Decimal(10, 2)), N'string', 1, 1, CAST(N'2025-05-17T09:53:24.970' AS DateTime));
INSERT [dbo].[Products] ([ProductId], [Name], [Description], [Price], [ImageUrl], [CategoryId], [IsAvailable], [CreatedAt])
VALUES (2, N'Nước Cam', N'Loại nước chứa nhiều vitamin C', CAST(10000.00 AS Decimal(10, 2)), N'https://suckhoedoisong.qltns.mediacdn.vn/324455921873985536/2023/11/7/uong-nuoc-cam-16993504421751885406385.jpg', 1, 1, CAST(N'2025-05-17T21:38:01.897' AS DateTime));
SET IDENTITY_INSERT [dbo].[Products] OFF;
GO

-- Dữ liệu cho bảng Users (chỉ chèn nếu bảng có đủ cột)
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Users]') AND type in (N'U'))
AND EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Users]') AND name = 'PasswordHash')
AND EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Users]') AND name = 'RefreshToken')
BEGIN
    SET IDENTITY_INSERT [dbo].[Users] ON;
    INSERT [dbo].[Users] ([UserId], [FullName], [Email], [PasswordHash], [Phone], [Address], [Gender], [BirthDate], [RoleId], [CreatedAt], [RefreshToken], [RefreshTokenExpiryTime])
    VALUES (1, N'Quang Đỗ', N'<EMAIL>', N'$2a$11$AVG1/BmKnaQym2y6XVLj2.Kax4hOLa9Xh6/NiReiVvLkGRWOMshjS', N'0366152345', N'Hồ Chí Minh', N'Male', CAST(N'2005-03-18' AS Date), 1, CAST(N'2025-05-17T09:49:44.333' AS DateTime), N'o/oN85J4KK6NhONr2m2LDTio3+RwkBj/1A9MGo7Hzs0=', CAST(N'2025-05-27T06:19:10.767' AS DateTime));
    INSERT [dbo].[Users] ([UserId], [FullName], [Email], [PasswordHash], [Phone], [Address], [Gender], [BirthDate], [RoleId], [CreatedAt], [RefreshToken], [RefreshTokenExpiryTime])
    VALUES (2, N'Đỗ', N'<EMAIL>', N'$2a$11$Z9ZKeFIY1SQPtM2TBK65IOJuJGLB11IM4V226IgN2Fc4wljef8xY2', N'0366501742', N'Xóm 9 Thôn Phú Long', N'Female', CAST(N'2005-05-17' AS Date), 2, CAST(N'2025-05-17T09:51:12.460' AS DateTime), N'Jup/CHokhS8+DH1rSgC7b+kUp6NNykraa6toBimyx9I=', CAST(N'2025-05-24T14:36:52.447' AS DateTime));
    INSERT [dbo].[Users] ([UserId], [FullName], [Email], [PasswordHash], [Phone], [Address], [Gender], [BirthDate], [RoleId], [CreatedAt], [RefreshToken], [RefreshTokenExpiryTime])
    VALUES (3, N'Admin', N'<EMAIL>', N'$2a$11$0vnR/Qv24P8TUzbDo6hFFuD5/DVhJt4ZVqLONVlaZjXWQUIWMeMJ2', N'0123456789', N'string', N'Male', CAST(N'2005-05-20' AS Date), 4, CAST(N'2025-05-20T13:14:47.170' AS DateTime), NULL, NULL);
    SET IDENTITY_INSERT [dbo].[Users] OFF;
END
GO

-- Dữ liệu cho bảng CartItems
SET IDENTITY_INSERT [dbo].[CartItems] ON;
INSERT [dbo].[CartItems] ([Id], [UserId], [ProductId], [Quantity]) VALUES (5, 1, 2, 50);
INSERT [dbo].[CartItems] ([Id], [UserId], [ProductId], [Quantity]) VALUES (6, 1, 1, 30);
SET IDENTITY_INSERT [dbo].[CartItems] OFF;
GO

-- Tạo Foreign Key Constraints
BEGIN TRY
    IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_CartItems_Products')
        ALTER TABLE [dbo].[CartItems] ADD CONSTRAINT [FK_CartItems_Products] FOREIGN KEY([ProductId]) REFERENCES [dbo].[Products] ([ProductId]) ON DELETE CASCADE;

    IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_CartItems_Users')
        ALTER TABLE [dbo].[CartItems] ADD CONSTRAINT [FK_CartItems_Users] FOREIGN KEY([UserId]) REFERENCES [dbo].[Users] ([UserId]) ON DELETE CASCADE;

    IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_WorkshopTickets_Workshops')
        ALTER TABLE [dbo].[WorkshopTickets] ADD CONSTRAINT [FK_WorkshopTickets_Workshops] FOREIGN KEY([WorkshopId]) REFERENCES [dbo].[Workshops] ([WorkshopId]);

    -- Thêm Check Constraint
    IF NOT EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_CartItems_Quantity')
        ALTER TABLE [dbo].[CartItems] ADD CONSTRAINT [CK_CartItems_Quantity] CHECK (([Quantity]>(0)));

    PRINT 'Foreign keys và constraints đã được tạo thành công!';
END TRY
BEGIN CATCH
    PRINT 'Một số foreign keys không thể tạo - có thể do dữ liệu không tương thích';
END CATCH
GO

PRINT 'Database script đã được thực thi thành công!';
PRINT 'Tất cả bảng, dữ liệu mẫu và constraints đã được tạo.';
GO
