import React from 'react';
import { Box, Card, CardContent, Typography, Chip } from '@mui/material';
import { useAppSelector } from '../stores/hooks';

const AuthDebug = () => {
  const { user, token, isAuthenticated, loading } = useAppSelector((state) => state.Authentication);

  return (
    <Card sx={{ mb: 3, border: '2px solid #ff9800' }}>
      <CardContent>
        <Typography variant="h6" sx={{ mb: 2, color: '#ff9800' }}>
          🔍 Auth Debug Info
        </Typography>
        
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant="body2" sx={{ fontWeight: 600, minWidth: 120 }}>
              Is Authenticated:
            </Typography>
            <Chip 
              label={String(isAuthenticated)} 
              color={isAuthenticated ? 'success' : 'error'} 
              size="small" 
            />
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant="body2" sx={{ fontWeight: 600, minWidth: 120 }}>
              Loading:
            </Typography>
            <Chip 
              label={String(loading)} 
              color={loading ? 'warning' : 'default'} 
              size="small" 
            />
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant="body2" sx={{ fontWeight: 600, minWidth: 120 }}>
              Has Token:
            </Typography>
            <Chip 
              label={token ? 'Yes' : 'No'} 
              color={token ? 'success' : 'error'} 
              size="small" 
            />
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant="body2" sx={{ fontWeight: 600, minWidth: 120 }}>
              User:
            </Typography>
            <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
              {user ? JSON.stringify(user, null, 2) : 'null'}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant="body2" sx={{ fontWeight: 600, minWidth: 120 }}>
              User Role ID:
            </Typography>
            <Chip 
              label={user?.roleId || 'N/A'} 
              color={user?.roleId === 4 ? 'success' : 'default'} 
              size="small" 
            />
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant="body2" sx={{ fontWeight: 600, minWidth: 120 }}>
              Is Admin:
            </Typography>
            <Chip 
              label={String(user?.roleId === 4)} 
              color={user?.roleId === 4 ? 'success' : 'error'} 
              size="small" 
            />
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant="body2" sx={{ fontWeight: 600, minWidth: 120 }}>
              LocalStorage Token:
            </Typography>
            <Typography variant="body2" sx={{ fontFamily: 'monospace', fontSize: '0.8rem' }}>
              {localStorage.getItem('token') ? 'Present' : 'Missing'}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant="body2" sx={{ fontWeight: 600, minWidth: 120 }}>
              LocalStorage User:
            </Typography>
            <Typography variant="body2" sx={{ fontFamily: 'monospace', fontSize: '0.8rem' }}>
              {localStorage.getItem('user') ? 'Present' : 'Missing'}
            </Typography>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

export default AuthDebug;
