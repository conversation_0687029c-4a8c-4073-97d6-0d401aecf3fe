import React, { useState, useEffect } from "react";
import {
  Con<PERSON>er,
  Paper,
  Typography,
  TextField,
  Button,
  Grid,
  Box,
  Avatar,
  Divider,
  Alert,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Card,
  CardContent,
  CardHeader,
  Chip,
  Stack,
  IconButton,
  Tooltip,
} from "@mui/material";
import {
  Person,
  Edit,
  Save,
  Cancel,
  Email,
  Phone,
  LocationOn,
  Cake,
  Wc,
  Lock,
  Visibility,
  VisibilityOff,
  AccountCircle,
  Security
} from "@mui/icons-material";
import { useAppSelector, useAppDispatch } from "../../stores/hooks";
import { updateProfile } from "../../stores/reducers/Authentication";
import { authAPI } from "../../services/api";
import { toast } from "react-toastify";

const ProfilePage = () => {
  const { user } = useAppSelector((state) => state.Authentication);
  const dispatch = useAppDispatch();

  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const [formData, setFormData] = useState({
    fullName: user?.fullName || "",
    email: user?.email || "",
    phone: user?.phone || "",
    address: user?.address || "",
    gender: user?.gender || "",
    birthDate: user?.birthDate ? user.birthDate.split('T')[0] : "",
  });

  const [passwordData, setPasswordData] = useState({
    oldPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  const [showPasswordForm, setShowPasswordForm] = useState(false);
  const [showPasswords, setShowPasswords] = useState({
    old: false,
    new: false,
    confirm: false,
  });

  useEffect(() => {
    if (user) {
      setFormData({
        fullName: user.fullName || "",
        email: user.email || "",
        phone: user.phone || "",
        address: user.address || "",
        gender: user.gender || "",
        birthDate: user.birthDate ? user.birthDate.split('T')[0] : "",
      });
    }
  }, [user]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePasswordChange = (field: string, value: string) => {
    setPasswordData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = async () => {
    setError("");
    setLoading(true);

    try {
      // Validate password if changing
      if (showPasswordForm) {
        if (!passwordData.oldPassword || !passwordData.newPassword || !passwordData.confirmPassword) {
          setError("Vui lòng điền đầy đủ thông tin mật khẩu");
          setLoading(false);
          return;
        }

        if (passwordData.newPassword !== passwordData.confirmPassword) {
          setError("Mật khẩu mới và xác nhận mật khẩu không khớp");
          setLoading(false);
          return;
        }

        if (passwordData.newPassword.length < 6) {
          setError("Mật khẩu mới phải có ít nhất 6 ký tự");
          setLoading(false);
          return;
        }
      }

      const updateData = {
        fullName: formData.fullName,
        phone: formData.phone,
        address: formData.address,
        gender: formData.gender,
        birthDate: formData.birthDate,
        ...(showPasswordForm && passwordData.newPassword ? {
          oldPassword: passwordData.oldPassword,
          newPassword: passwordData.newPassword,
          confirmPassword: passwordData.confirmPassword
        } : {})
      };

      // Call update API (returns success message)
      await authAPI.updateProfile(updateData);

      // Get fresh user data after update
      const updatedUser = await authAPI.getProfile();
      dispatch(updateProfile(updatedUser));

      setIsEditing(false);
      setShowPasswordForm(false);
      setPasswordData({ oldPassword: "", newPassword: "", confirmPassword: "" });
      toast.success("Cập nhật thông tin thành công!");
    } catch (error: any) {
      const errorMessage = error.message || "Có lỗi xảy ra khi cập nhật thông tin";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    setShowPasswordForm(false);
    setError("");
    setPasswordData({ oldPassword: "", newPassword: "", confirmPassword: "" });
    setShowPasswords({ old: false, new: false, confirm: false });

    if (user) {
      setFormData({
        fullName: user.fullName || "",
        email: user.email || "",
        phone: user.phone || "",
        address: user.address || "",
        gender: user.gender || "",
        birthDate: user.birthDate ? user.birthDate.split('T')[0] : "",
      });
    }
  };

  const togglePasswordVisibility = (field: 'old' | 'new' | 'confirm') => {
    setShowPasswords(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  const getRoleDisplayName = (roleId: number) => {
    switch (roleId) {
      case 1: return "Khách hàng";
      case 2: return "Nhân viên";
      case 4: return "Quản trị viên";
      default: return "Khách";
    }
  };

  const getRoleColor = (roleId: number) => {
    switch (roleId) {
      case 1: return "primary";
      case 2: return "secondary";
      case 4: return "error";
      default: return "default";
    }
  };

  if (!user) {
    return (
      <Container maxWidth="md" sx={{ mt: 4 }}>
        <Alert severity="warning">
          Vui lòng đăng nhập để xem thông tin cá nhân
        </Alert>
      </Container>
    );
  }

  return (
    <Box sx={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
      py: 4
    }}>
      <Container maxWidth="lg">
        {/* Header Section */}
        <Card
          elevation={8}
          sx={{
            mb: 4,
            background: 'linear-gradient(135deg, #9e655c 0%, #8b4a47 100%)',
            borderRadius: 4,
            overflow: 'hidden',
            position: 'relative',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="4"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
              opacity: 0.3
            }
          }}
        >
          <CardContent sx={{ p: 5, position: 'relative', zIndex: 1 }}>
            <Box display="flex" alignItems="center" justifyContent="space-between" flexWrap="wrap" gap={3}>
              <Box display="flex" alignItems="center" gap={4}>
                <Box position="relative">
                  <Avatar
                    sx={{
                      width: 120,
                      height: 120,
                      bgcolor: "rgba(255,255,255,0.15)",
                      border: "4px solid rgba(255,255,255,0.3)",
                      boxShadow: '0 8px 32px rgba(0,0,0,0.2)',
                      backdropFilter: 'blur(10px)'
                    }}
                  >
                    <AccountCircle sx={{ fontSize: 70, color: "white" }} />
                  </Avatar>
                  <Box
                    sx={{
                      position: 'absolute',
                      bottom: 0,
                      right: 0,
                      width: 32,
                      height: 32,
                      borderRadius: '50%',
                      bgcolor: 'success.main',
                      border: '3px solid white',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                  >
                    <Box sx={{ width: 8, height: 8, borderRadius: '50%', bgcolor: 'white' }} />
                  </Box>
                </Box>
                <Box>
                  <Typography variant="h3" sx={{
                    color: "white",
                    fontWeight: 700,
                    mb: 1,
                    textShadow: '0 2px 4px rgba(0,0,0,0.3)'
                  }}>
                    {user?.fullName || "Người dùng"}
                  </Typography>
                  <Stack direction="row" spacing={2} alignItems="center" mb={2}>
                    <Chip
                      label={getRoleDisplayName(user?.roleId || 1)}
                      color={getRoleColor(user?.roleId || 1) as any}
                      size="medium"
                      sx={{
                        fontWeight: 600,
                        fontSize: '0.875rem',
                        boxShadow: '0 2px 8px rgba(0,0,0,0.2)'
                      }}
                    />
                  </Stack>
                  <Typography variant="body1" sx={{
                    color: "rgba(255,255,255,0.9)",
                    display: 'flex',
                    alignItems: 'center',
                    fontSize: '1.1rem'
                  }}>
                    <Email sx={{ fontSize: 20, mr: 1 }} />
                    {user?.email}
                  </Typography>
                </Box>
              </Box>
              {!isEditing && (
                <Button
                  variant="contained"
                  size="large"
                  startIcon={<Edit />}
                  onClick={() => setIsEditing(true)}
                  sx={{
                    bgcolor: "rgba(255,255,255,0.2)",
                    color: "white",
                    backdropFilter: 'blur(10px)',
                    border: '1px solid rgba(255,255,255,0.3)',
                    px: 4,
                    py: 1.5,
                    fontSize: '1rem',
                    fontWeight: 600,
                    borderRadius: 3,
                    boxShadow: '0 4px 16px rgba(0,0,0,0.2)',
                    '&:hover': {
                      bgcolor: "rgba(255,255,255,0.3)",
                      transform: 'translateY(-2px)',
                      boxShadow: '0 6px 20px rgba(0,0,0,0.3)'
                    },
                    transition: 'all 0.3s ease'
                  }}
                >
                  Chỉnh sửa thông tin
                </Button>
              )}
            </Box>
          </CardContent>
        </Card>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

        <Grid container spacing={4}>
          {/* Personal Information Card */}
          <Grid item xs={12} md={8}>
            <Card
              elevation={6}
              sx={{
                borderRadius: 3,
                overflow: 'hidden',
                background: 'linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)',
                border: '1px solid rgba(158, 101, 92, 0.1)'
              }}
            >
              <CardHeader
                title={
                  <Typography variant="h5" sx={{ fontWeight: 600, color: '#9e655c' }}>
                    Thông tin cá nhân
                  </Typography>
                }
                avatar={
                  <Box sx={{
                    p: 1,
                    borderRadius: 2,
                    bgcolor: 'rgba(158, 101, 92, 0.1)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <Person sx={{ color: "#9e655c", fontSize: 28 }} />
                  </Box>
                }
                sx={{
                  bgcolor: "rgba(158, 101, 92, 0.05)",
                  borderBottom: '1px solid rgba(158, 101, 92, 0.1)'
                }}
              />
              <CardContent sx={{ p: 4 }}>
                <Grid container spacing={4}>
                  <Grid item xs={12} sm={6}>
                    <Box sx={{ position: 'relative' }}>
                      <Typography variant="caption" sx={{
                        color: 'text.secondary',
                        fontWeight: 500,
                        mb: 1,
                        display: 'block'
                      }}>
                        Họ và tên
                      </Typography>
                      <TextField
                        fullWidth
                        value={formData.fullName}
                        onChange={(e) => handleInputChange("fullName", e.target.value)}
                        disabled={!isEditing}
                        variant={isEditing ? "outlined" : "standard"}
                        sx={{
                          '& .MuiInputBase-root': {
                            bgcolor: isEditing ? 'white' : 'transparent',
                            borderRadius: 2,
                            fontSize: '1.1rem',
                            fontWeight: 500
                          },
                          '& .MuiInputBase-input': {
                            py: 1.5,
                            pl: 5
                          }
                        }}
                        InputProps={{
                          startAdornment: (
                            <Box sx={{ position: 'absolute', left: 12, zIndex: 1 }}>
                              <Person sx={{ color: "#9e655c", fontSize: 20 }} />
                            </Box>
                          )
                        }}
                      />
                    </Box>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Box sx={{ position: 'relative' }}>
                      <Typography variant="caption" sx={{
                        color: 'text.secondary',
                        fontWeight: 500,
                        mb: 1,
                        display: 'block'
                      }}>
                        Email
                      </Typography>
                      <TextField
                        fullWidth
                        value={formData.email}
                        disabled={true}
                        variant="standard"
                        helperText="Email không thể thay đổi"
                        sx={{
                          '& .MuiInputBase-root': {
                            bgcolor: 'rgba(0,0,0,0.02)',
                            borderRadius: 2,
                            fontSize: '1.1rem',
                            fontWeight: 500
                          },
                          '& .MuiInputBase-input': {
                            py: 1.5,
                            pl: 5
                          }
                        }}
                        InputProps={{
                          startAdornment: (
                            <Box sx={{ position: 'absolute', left: 12, zIndex: 1 }}>
                              <Email sx={{ color: "#9e655c", fontSize: 20 }} />
                            </Box>
                          )
                        }}
                      />
                    </Box>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Box sx={{ position: 'relative' }}>
                      <Typography variant="caption" sx={{
                        color: 'text.secondary',
                        fontWeight: 500,
                        mb: 1,
                        display: 'block'
                      }}>
                        Số điện thoại
                      </Typography>
                      <TextField
                        fullWidth
                        value={formData.phone}
                        onChange={(e) => handleInputChange("phone", e.target.value)}
                        disabled={!isEditing}
                        variant={isEditing ? "outlined" : "standard"}
                        sx={{
                          '& .MuiInputBase-root': {
                            bgcolor: isEditing ? 'white' : 'transparent',
                            borderRadius: 2,
                            fontSize: '1.1rem',
                            fontWeight: 500
                          },
                          '& .MuiInputBase-input': {
                            py: 1.5,
                            pl: 5
                          }
                        }}
                        InputProps={{
                          startAdornment: (
                            <Box sx={{ position: 'absolute', left: 12, zIndex: 1 }}>
                              <Phone sx={{ color: "#9e655c", fontSize: 20 }} />
                            </Box>
                          )
                        }}
                      />
                    </Box>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Box sx={{ position: 'relative' }}>
                      <Typography variant="caption" sx={{
                        color: 'text.secondary',
                        fontWeight: 500,
                        mb: 1,
                        display: 'block'
                      }}>
                        Giới tính
                      </Typography>
                      <FormControl fullWidth disabled={!isEditing}>
                        <Select
                          value={formData.gender}
                          onChange={(e) => handleInputChange("gender", e.target.value)}
                          variant={isEditing ? "outlined" : "standard"}
                          sx={{
                            '& .MuiInputBase-root': {
                              bgcolor: isEditing ? 'white' : 'transparent',
                              borderRadius: 2,
                              fontSize: '1.1rem',
                              fontWeight: 500
                            },
                            '& .MuiSelect-select': {
                              py: 1.5,
                              pl: 5
                            }
                          }}
                          startAdornment={
                            <Box sx={{ position: 'absolute', left: 12, zIndex: 1 }}>
                              <Wc sx={{ color: "#9e655c", fontSize: 20 }} />
                            </Box>
                          }
                        >
                          <MenuItem value="Male">Nam</MenuItem>
                          <MenuItem value="Female">Nữ</MenuItem>
                          <MenuItem value="Other">Khác</MenuItem>
                        </Select>
                      </FormControl>
                    </Box>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Box sx={{ position: 'relative' }}>
                      <Typography variant="caption" sx={{
                        color: 'text.secondary',
                        fontWeight: 500,
                        mb: 1,
                        display: 'block'
                      }}>
                        Ngày sinh
                      </Typography>
                      <TextField
                        fullWidth
                        type="date"
                        value={formData.birthDate}
                        onChange={(e) => handleInputChange("birthDate", e.target.value)}
                        disabled={!isEditing}
                        variant={isEditing ? "outlined" : "standard"}
                        InputLabelProps={{ shrink: true }}
                        sx={{
                          '& .MuiInputBase-root': {
                            bgcolor: isEditing ? 'white' : 'transparent',
                            borderRadius: 2,
                            fontSize: '1.1rem',
                            fontWeight: 500
                          },
                          '& .MuiInputBase-input': {
                            py: 1.5,
                            pl: 5
                          }
                        }}
                        InputProps={{
                          startAdornment: (
                            <Box sx={{ position: 'absolute', left: 12, zIndex: 1 }}>
                              <Cake sx={{ color: "#9e655c", fontSize: 20 }} />
                            </Box>
                          )
                        }}
                      />
                    </Box>
                  </Grid>

                  <Grid item xs={12}>
                    <Box sx={{ position: 'relative' }}>
                      <Typography variant="caption" sx={{
                        color: 'text.secondary',
                        fontWeight: 500,
                        mb: 1,
                        display: 'block'
                      }}>
                        Địa chỉ
                      </Typography>
                      <TextField
                        fullWidth
                        value={formData.address}
                        onChange={(e) => handleInputChange("address", e.target.value)}
                        disabled={!isEditing}
                        variant={isEditing ? "outlined" : "standard"}
                        multiline
                        rows={2}
                        sx={{
                          '& .MuiInputBase-root': {
                            bgcolor: isEditing ? 'white' : 'transparent',
                            borderRadius: 2,
                            fontSize: '1.1rem',
                            fontWeight: 500
                          },
                          '& .MuiInputBase-input': {
                            py: 1.5,
                            pl: 5
                          }
                        }}
                        InputProps={{
                          startAdornment: (
                            <Box sx={{ position: 'absolute', left: 12, top: 12, zIndex: 1 }}>
                              <LocationOn sx={{ color: "#9e655c", fontSize: 20 }} />
                            </Box>
                          )
                        }}
                      />
                    </Box>
                  </Grid>
                </Grid>

                {isEditing && (
                  <Box mt={4} display="flex" justifyContent="flex-end" gap={2}>
                    <Button
                      variant="outlined"
                      size="large"
                      startIcon={<Cancel />}
                      onClick={handleCancel}
                      disabled={loading}
                      sx={{
                        borderColor: '#9e655c',
                        color: '#9e655c',
                        px: 3,
                        py: 1.5,
                        borderRadius: 3,
                        '&:hover': {
                          borderColor: '#8b4a47',
                          bgcolor: 'rgba(158, 101, 92, 0.05)'
                        }
                      }}
                    >
                      Hủy bỏ
                    </Button>
                    <Button
                      variant="contained"
                      size="large"
                      startIcon={<Save />}
                      onClick={handleSave}
                      disabled={loading}
                      sx={{
                        bgcolor: '#9e655c',
                        px: 3,
                        py: 1.5,
                        borderRadius: 3,
                        boxShadow: '0 4px 16px rgba(158, 101, 92, 0.3)',
                        '&:hover': {
                          bgcolor: '#8b4a47',
                          boxShadow: '0 6px 20px rgba(158, 101, 92, 0.4)'
                        }
                      }}
                    >
                      {loading ? <CircularProgress size={24} color="inherit" /> : "Lưu thay đổi"}
                    </Button>
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* Security Settings Card */}
          <Grid item xs={12} md={4}>
            <Card
              elevation={6}
              sx={{
                borderRadius: 3,
                overflow: 'hidden',
                background: 'linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)',
                border: '1px solid rgba(158, 101, 92, 0.1)',
                height: 'fit-content'
              }}
            >
              <CardHeader
                title={
                  <Typography variant="h5" sx={{ fontWeight: 600, color: '#9e655c' }}>
                    Bảo mật
                  </Typography>
                }
                avatar={
                  <Box sx={{
                    p: 1,
                    borderRadius: 2,
                    bgcolor: 'rgba(158, 101, 92, 0.1)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <Security sx={{ color: "#9e655c", fontSize: 28 }} />
                  </Box>
                }
                sx={{
                  bgcolor: "rgba(158, 101, 92, 0.05)",
                  borderBottom: '1px solid rgba(158, 101, 92, 0.1)'
                }}
              />
              <CardContent sx={{ p: 4 }}>
                {!showPasswordForm ? (
                  <Box textAlign="center">
                    <Box sx={{
                      width: 80,
                      height: 80,
                      borderRadius: '50%',
                      bgcolor: 'rgba(158, 101, 92, 0.1)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mx: 'auto',
                      mb: 3
                    }}>
                      <Lock sx={{ fontSize: 40, color: "#9e655c" }} />
                    </Box>
                    <Typography variant="h6" sx={{ fontWeight: 600, mb: 2, color: '#9e655c' }}>
                      Bảo mật tài khoản
                    </Typography>
                    <Typography variant="body2" color="text.secondary" mb={4} sx={{ lineHeight: 1.6 }}>
                      Thay đổi mật khẩu định kỳ để bảo vệ tài khoản của bạn khỏi các truy cập trái phép
                    </Typography>
                    <Button
                      variant="contained"
                      size="large"
                      startIcon={<Lock />}
                      onClick={() => setShowPasswordForm(true)}
                      fullWidth
                      sx={{
                        bgcolor: '#9e655c',
                        py: 1.5,
                        borderRadius: 3,
                        boxShadow: '0 4px 16px rgba(158, 101, 92, 0.3)',
                        '&:hover': {
                          bgcolor: '#8b4a47',
                          boxShadow: '0 6px 20px rgba(158, 101, 92, 0.4)'
                        }
                      }}
                    >
                      Đổi mật khẩu
                    </Button>
                  </Box>
                ) : (
                  <Box>
                    <Typography variant="h6" sx={{ fontWeight: 600, mb: 3, color: '#9e655c' }}>
                      Thay đổi mật khẩu
                    </Typography>

                    <Box sx={{ mb: 3 }}>
                      <Typography variant="caption" sx={{
                        color: 'text.secondary',
                        fontWeight: 500,
                        mb: 1,
                        display: 'block'
                      }}>
                        Mật khẩu hiện tại
                      </Typography>
                      <TextField
                        fullWidth
                        type={showPasswords.old ? "text" : "password"}
                        value={passwordData.oldPassword}
                        onChange={(e) => handlePasswordChange("oldPassword", e.target.value)}
                        variant="outlined"
                        sx={{
                          '& .MuiInputBase-root': {
                            borderRadius: 2,
                            fontSize: '1rem'
                          },
                          '& .MuiInputBase-input': {
                            py: 1.5,
                            pl: 5
                          }
                        }}
                        InputProps={{
                          startAdornment: (
                            <Box sx={{ position: 'absolute', left: 12, zIndex: 1 }}>
                              <Lock sx={{ color: "#9e655c", fontSize: 20 }} />
                            </Box>
                          ),
                          endAdornment: (
                            <IconButton
                              onClick={() => togglePasswordVisibility('old')}
                              edge="end"
                              size="small"
                              sx={{ mr: 1 }}
                            >
                              {showPasswords.old ? <VisibilityOff /> : <Visibility />}
                            </IconButton>
                          )
                        }}
                      />
                    </Box>

                    <Box sx={{ mb: 3 }}>
                      <Typography variant="caption" sx={{
                        color: 'text.secondary',
                        fontWeight: 500,
                        mb: 1,
                        display: 'block'
                      }}>
                        Mật khẩu mới
                      </Typography>
                      <TextField
                        fullWidth
                        type={showPasswords.new ? "text" : "password"}
                        value={passwordData.newPassword}
                        onChange={(e) => handlePasswordChange("newPassword", e.target.value)}
                        variant="outlined"
                        sx={{
                          '& .MuiInputBase-root': {
                            borderRadius: 2,
                            fontSize: '1rem'
                          },
                          '& .MuiInputBase-input': {
                            py: 1.5,
                            pl: 5
                          }
                        }}
                        InputProps={{
                          startAdornment: (
                            <Box sx={{ position: 'absolute', left: 12, zIndex: 1 }}>
                              <Lock sx={{ color: "#9e655c", fontSize: 20 }} />
                            </Box>
                          ),
                          endAdornment: (
                            <IconButton
                              onClick={() => togglePasswordVisibility('new')}
                              edge="end"
                              size="small"
                              sx={{ mr: 1 }}
                            >
                              {showPasswords.new ? <VisibilityOff /> : <Visibility />}
                            </IconButton>
                          )
                        }}
                      />
                    </Box>

                    <Box sx={{ mb: 4 }}>
                      <Typography variant="caption" sx={{
                        color: 'text.secondary',
                        fontWeight: 500,
                        mb: 1,
                        display: 'block'
                      }}>
                        Xác nhận mật khẩu mới
                      </Typography>
                      <TextField
                        fullWidth
                        type={showPasswords.confirm ? "text" : "password"}
                        value={passwordData.confirmPassword}
                        onChange={(e) => handlePasswordChange("confirmPassword", e.target.value)}
                        variant="outlined"
                        sx={{
                          '& .MuiInputBase-root': {
                            borderRadius: 2,
                            fontSize: '1rem'
                          },
                          '& .MuiInputBase-input': {
                            py: 1.5,
                            pl: 5
                          }
                        }}
                        InputProps={{
                          startAdornment: (
                            <Box sx={{ position: 'absolute', left: 12, zIndex: 1 }}>
                              <Lock sx={{ color: "#9e655c", fontSize: 20 }} />
                            </Box>
                          ),
                          endAdornment: (
                            <IconButton
                              onClick={() => togglePasswordVisibility('confirm')}
                              edge="end"
                              size="small"
                              sx={{ mr: 1 }}
                            >
                              {showPasswords.confirm ? <VisibilityOff /> : <Visibility />}
                            </IconButton>
                          )
                        }}
                      />
                    </Box>

                    <Stack direction="row" spacing={2}>
                      <Button
                        variant="outlined"
                        size="large"
                        onClick={() => {
                          setShowPasswordForm(false);
                          setPasswordData({ oldPassword: "", newPassword: "", confirmPassword: "" });
                          setShowPasswords({ old: false, new: false, confirm: false });
                        }}
                        disabled={loading}
                        fullWidth
                        sx={{
                          borderColor: '#9e655c',
                          color: '#9e655c',
                          py: 1.5,
                          borderRadius: 3,
                          '&:hover': {
                            borderColor: '#8b4a47',
                            bgcolor: 'rgba(158, 101, 92, 0.05)'
                          }
                        }}
                      >
                        Hủy
                      </Button>
                      <Button
                        variant="contained"
                        size="large"
                        onClick={handleSave}
                        disabled={loading || !passwordData.oldPassword || !passwordData.newPassword || !passwordData.confirmPassword}
                        fullWidth
                        sx={{
                          bgcolor: '#9e655c',
                          py: 1.5,
                          borderRadius: 3,
                          boxShadow: '0 4px 16px rgba(158, 101, 92, 0.3)',
                          '&:hover': {
                            bgcolor: '#8b4a47',
                            boxShadow: '0 6px 20px rgba(158, 101, 92, 0.4)'
                          }
                        }}
                      >
                        {loading ? <CircularProgress size={20} color="inherit" /> : "Lưu"}
                      </Button>
                    </Stack>
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

export default ProfilePage;
