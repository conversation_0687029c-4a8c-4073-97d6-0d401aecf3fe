/****** Object:  Database [Exe202]    Script Date: 5/20/2025 10:21:44 PM ******/
-- Tạo database chỉ khi chưa tồn tại
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'Exe202')
BEGIN
    CREATE DATABASE [Exe202];
END
GO

-- Sử dụng database
USE [Exe202];
GO

-- Thiết lập compatibility level hợp lệ (160 thay vì 170)
ALTER DATABASE [Exe202] SET COMPATIBILITY_LEVEL = 160;
GO
-- Bỏ qua các lệnh ALTER DATABASE vì user không có quyền
-- Chỉ giữ lại những thiết lập cần thiết cho session hiện tại
SET ANSI_NULLS ON;
GO
SET QUOTED_IDENTIFIER ON;
GO
/*** The scripts of database scoped configurations in Azure should be executed inside the target database connection. ***/
GO
-- ALTER DATABASE SCOPED CONFIGURATION SET MAXDOP = 8;
GO
/****** Object:  Table [dbo].[CartItems]    Script Date: 5/20/2025 10:21:44 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[CartItems](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[UserId] [int] NOT NULL,
	[ProductId] [int] NOT NULL,
	[Quantity] [int] NOT NULL,
PRIMARY KEY CLUSTERED
(
	[Id] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Categories]    Script Date: 5/20/2025 10:21:44 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Categories](
	[CategoryId] [int] IDENTITY(1,1) NOT NULL,
	[Name] [nvarchar](50) NULL,
	[Description] [nvarchar](255) NULL,
PRIMARY KEY CLUSTERED
(
	[CategoryId] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Feedbacks]    Script Date: 5/20/2025 10:21:44 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Feedbacks](
	[FeedbackId] [int] IDENTITY(1,1) NOT NULL,
	[UserId] [int] NOT NULL,
	[ProductId] [int] NULL,
	[WorkshopId] [int] NULL,
	[Rating] [int] NULL,
	[Comment] [nvarchar](1000) NULL,
	[CreatedAt] [datetime] NULL,
PRIMARY KEY CLUSTERED
(
	[FeedbackId] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[OrderDetails]    Script Date: 5/20/2025 10:21:44 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[OrderDetails](
	[OrderDetailId] [int] IDENTITY(1,1) NOT NULL,
	[OrderId] [int] NULL,
	[ProductId] [int] NULL,
	[ProductDetailId] [int] NULL,
	[Quantity] [int] NULL,
	[UnitPrice] [decimal](10, 2) NULL,
PRIMARY KEY CLUSTERED
(
	[OrderDetailId] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Orders]    Script Date: 5/20/2025 10:21:44 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Orders](
	[OrderId] [int] IDENTITY(1,1) NOT NULL,
	[UserId] [int] NULL,
	[OrderDate] [datetime] NULL,
	[TotalAmount] [decimal](10, 2) NULL,
	[DiscountAmount] [decimal](10, 2) NULL,
	[FinalAmount]  AS ([TotalAmount]-[DiscountAmount]) PERSISTED,
	[Status] [nvarchar](50) NULL,
	[Note] [nvarchar](255) NULL,
	[DeliveryAddress] [nvarchar](255) NULL,
	[PromotionCode] [nvarchar](50) NULL,
PRIMARY KEY CLUSTERED
(
	[OrderId] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Payments]    Script Date: 5/20/2025 10:21:44 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- Kiểm tra và tạo bảng Payments nếu chưa tồn tại
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Payments]') AND type in (N'U'))
BEGIN
CREATE TABLE [dbo].[Payments](
	[PaymentId] [int] IDENTITY(1,1) NOT NULL,
	[OrderId] [int] NULL,
	[PaymentMethod] [nvarchar](50) NULL,
	[PaidAmount] [decimal](10, 2) NULL,
	[PaymentDate] [datetime] NULL,
	[Status] [nvarchar](50) NULL,
PRIMARY KEY CLUSTERED
(
	[PaymentId] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
END
GO
/****** Object:  Table [dbo].[ProductDetails]    Script Date: 5/20/2025 10:21:44 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ProductDetails](
	[ProductDetailId] [int] IDENTITY(1,1) NOT NULL,
	[ProductId] [int] NULL,
	[Size] [nvarchar](20) NULL,
	[Color] [nvarchar](50) NULL,
	[FlowerType] [nvarchar](50) NULL,
	[ExtraPrice] [decimal](10, 2) NULL,
PRIMARY KEY CLUSTERED
(
	[ProductDetailId] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Products]    Script Date: 5/20/2025 10:21:44 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Products](
	[ProductId] [int] IDENTITY(1,1) NOT NULL,
	[Name] [nvarchar](100) NULL,
	[Description] [nvarchar](255) NULL,
	[Price] [decimal](10, 2) NULL,
	[ImageUrl] [nvarchar](255) NULL,
	[CategoryId] [int] NULL,
	[IsAvailable] [bit] NULL,
	[CreatedAt] [datetime] NULL,
PRIMARY KEY CLUSTERED
(
	[ProductId] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Promotions]    Script Date: 5/20/2025 10:21:44 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Promotions](
	[PromotionId] [int] IDENTITY(1,1) NOT NULL,
	[Code] [nvarchar](50) NULL,
	[Description] [nvarchar](255) NULL,
	[DiscountPercent] [int] NULL,
	[MaxDiscount] [decimal](10, 2) NULL,
	[StartDate] [date] NULL,
	[EndDate] [date] NULL,
	[IsActive] [bit] NULL,
PRIMARY KEY CLUSTERED
(
	[PromotionId] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Roles]    Script Date: 5/20/2025 10:21:44 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Roles](
	[RoleId] [int] IDENTITY(1,1) NOT NULL,
	[RoleName] [nvarchar](50) NOT NULL,
PRIMARY KEY CLUSTERED
(
	[RoleId] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Users]    Script Date: 5/20/2025 10:21:44 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- Kiểm tra và tạo bảng Users nếu chưa tồn tại
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Users]') AND type in (N'U'))
BEGIN
CREATE TABLE [dbo].[Users](
	[UserId] [int] IDENTITY(1,1) NOT NULL,
	[FullName] [nvarchar](100) NOT NULL,
	[Email] [nvarchar](100) NOT NULL,
	[PasswordHash] [nvarchar](255) NOT NULL,
	[Phone] [nvarchar](20) NULL,
	[Address] [nvarchar](255) NULL,
	[Gender] [nvarchar](10) NULL,
	[BirthDate] [date] NULL,
	[RoleId] [int] NOT NULL,
	[CreatedAt] [datetime] NULL,
	[RefreshToken] [nvarchar](500) NULL,
	[RefreshTokenExpiryTime] [datetime] NULL,
PRIMARY KEY CLUSTERED
(
	[UserId] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
END
GO
/****** Object:  Table [dbo].[UserWorkshopTickets]    Script Date: 5/20/2025 10:21:44 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[UserWorkshopTickets](
	[UserTicketId] [int] IDENTITY(1,1) NOT NULL,
	[UserId] [int] NULL,
	[TicketId] [int] NULL,
	[Quantity] [int] NULL,
	[PurchaseDate] [datetime] NULL,
PRIMARY KEY CLUSTERED
(
	[UserTicketId] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Workshops]    Script Date: 5/20/2025 10:21:44 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Workshops](
	[WorkshopId] [int] IDENTITY(1,1) NOT NULL,
	[Title] [nvarchar](100) NULL,
	[Description] [nvarchar](255) NULL,
	[Location] [nvarchar](255) NULL,
	[EventDate] [datetime] NULL,
	[MaxAttendees] [int] NULL,
	[Price] [decimal](10, 2) NULL,
	[ImageUrl] [nvarchar](255) NULL,
	[CreatedAt] [datetime] NULL,
PRIMARY KEY CLUSTERED
(
	[WorkshopId] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[WorkshopTickets]    Script Date: 5/20/2025 10:21:44 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[WorkshopTickets](
	[TicketId] [int] IDENTITY(1,1) NOT NULL,
	[WorkshopId] [int] NULL,
	[TicketType] [nvarchar](50) NULL,
	[Price] [decimal](10, 2) NULL,
	[Quantity] [int] NULL,
PRIMARY KEY CLUSTERED
(
	[TicketId] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
SET IDENTITY_INSERT [dbo].[CartItems] ON
GO
INSERT [dbo].[CartItems] ([Id], [UserId], [ProductId], [Quantity]) VALUES (5, 1, 2, 50)
GO
INSERT [dbo].[CartItems] ([Id], [UserId], [ProductId], [Quantity]) VALUES (6, 1, 1, 30)
GO
SET IDENTITY_INSERT [dbo].[CartItems] OFF
GO
SET IDENTITY_INSERT [dbo].[Categories] ON
GO
INSERT [dbo].[Categories] ([CategoryId], [Name], [Description]) VALUES (1, N'Nước Uống', N'Các Loại Nước Mùa Hè')
GO
SET IDENTITY_INSERT [dbo].[Categories] OFF
GO
SET IDENTITY_INSERT [dbo].[OrderDetails] ON
GO
INSERT [dbo].[OrderDetails] ([OrderDetailId], [OrderId], [ProductId], [ProductDetailId], [Quantity], [UnitPrice]) VALUES (1, 1, 1, NULL, 100, CAST(10000.00 AS Decimal(10, 2)))
GO
INSERT [dbo].[OrderDetails] ([OrderDetailId], [OrderId], [ProductId], [ProductDetailId], [Quantity], [UnitPrice]) VALUES (2, 2, 1, NULL, 10, CAST(10000.00 AS Decimal(10, 2)))
GO
INSERT [dbo].[OrderDetails] ([OrderDetailId], [OrderId], [ProductId], [ProductDetailId], [Quantity], [UnitPrice]) VALUES (3, 3, 1, NULL, 10, CAST(10000.00 AS Decimal(10, 2)))
GO
INSERT [dbo].[OrderDetails] ([OrderDetailId], [OrderId], [ProductId], [ProductDetailId], [Quantity], [UnitPrice]) VALUES (4, 4, 2, NULL, 20, CAST(10000.00 AS Decimal(10, 2)))
GO
INSERT [dbo].[OrderDetails] ([OrderDetailId], [OrderId], [ProductId], [ProductDetailId], [Quantity], [UnitPrice]) VALUES (5, 5, 2, NULL, 20, CAST(10000.00 AS Decimal(10, 2)))
GO
INSERT [dbo].[OrderDetails] ([OrderDetailId], [OrderId], [ProductId], [ProductDetailId], [Quantity], [UnitPrice]) VALUES (6, 6, 2, NULL, 20, CAST(10000.00 AS Decimal(10, 2)))
GO
INSERT [dbo].[OrderDetails] ([OrderDetailId], [OrderId], [ProductId], [ProductDetailId], [Quantity], [UnitPrice]) VALUES (7, 7, 2, NULL, 20, CAST(10000.00 AS Decimal(10, 2)))
GO
INSERT [dbo].[OrderDetails] ([OrderDetailId], [OrderId], [ProductId], [ProductDetailId], [Quantity], [UnitPrice]) VALUES (8, 8, 2, NULL, 20, CAST(10000.00 AS Decimal(10, 2)))
GO
INSERT [dbo].[OrderDetails] ([OrderDetailId], [OrderId], [ProductId], [ProductDetailId], [Quantity], [UnitPrice]) VALUES (9, 9, 1, NULL, 30, CAST(10000.00 AS Decimal(10, 2)))
GO
INSERT [dbo].[OrderDetails] ([OrderDetailId], [OrderId], [ProductId], [ProductDetailId], [Quantity], [UnitPrice]) VALUES (10, 10, 1, NULL, 30, CAST(10000.00 AS Decimal(10, 2)))
GO
INSERT [dbo].[OrderDetails] ([OrderDetailId], [OrderId], [ProductId], [ProductDetailId], [Quantity], [UnitPrice]) VALUES (11, 11, 1, NULL, 30, CAST(10000.00 AS Decimal(10, 2)))
GO
INSERT [dbo].[OrderDetails] ([OrderDetailId], [OrderId], [ProductId], [ProductDetailId], [Quantity], [UnitPrice]) VALUES (12, 12, 1, NULL, 30, CAST(10000.00 AS Decimal(10, 2)))
GO
INSERT [dbo].[OrderDetails] ([OrderDetailId], [OrderId], [ProductId], [ProductDetailId], [Quantity], [UnitPrice]) VALUES (13, 13, 2, NULL, 50, CAST(10000.00 AS Decimal(10, 2)))
GO
INSERT [dbo].[OrderDetails] ([OrderDetailId], [OrderId], [ProductId], [ProductDetailId], [Quantity], [UnitPrice]) VALUES (14, 14, 2, NULL, 50, CAST(10000.00 AS Decimal(10, 2)))
GO
INSERT [dbo].[OrderDetails] ([OrderDetailId], [OrderId], [ProductId], [ProductDetailId], [Quantity], [UnitPrice]) VALUES (15, 15, 2, NULL, 50, CAST(10000.00 AS Decimal(10, 2)))
GO
INSERT [dbo].[OrderDetails] ([OrderDetailId], [OrderId], [ProductId], [ProductDetailId], [Quantity], [UnitPrice]) VALUES (16, 16, 2, NULL, 50, CAST(10000.00 AS Decimal(10, 2)))
GO
SET IDENTITY_INSERT [dbo].[OrderDetails] OFF
GO
SET IDENTITY_INSERT [dbo].[Orders] ON
GO
INSERT [dbo].[Orders] ([OrderId], [UserId], [OrderDate], [TotalAmount], [DiscountAmount], [Status], [Note], [DeliveryAddress], [PromotionCode]) VALUES (1, 1, CAST(N'2025-05-19T14:27:08.670' AS DateTime), CAST(1000000.00 AS Decimal(10, 2)), CAST(0.00 AS Decimal(10, 2)), N'Pending', N'string', N'Xóm 9 Thôn Phú Long', N'')
GO
INSERT [dbo].[Orders] ([OrderId], [UserId], [OrderDate], [TotalAmount], [DiscountAmount], [Status], [Note], [DeliveryAddress], [PromotionCode]) VALUES (2, NULL, CAST(N'2025-05-19T15:36:32.890' AS DateTime), CAST(100000.00 AS Decimal(10, 2)), CAST(0.00 AS Decimal(10, 2)), N'Pending', N'string', N'Xóm 12', N'')
GO
INSERT [dbo].[Orders] ([OrderId], [UserId], [OrderDate], [TotalAmount], [DiscountAmount], [Status], [Note], [DeliveryAddress], [PromotionCode]) VALUES (3, NULL, CAST(N'2025-05-20T00:26:05.993' AS DateTime), CAST(100000.00 AS Decimal(10, 2)), CAST(0.00 AS Decimal(10, 2)), N'Pending', N'string', N'Xóm 9', N'')
GO
INSERT [dbo].[Orders] ([OrderId], [UserId], [OrderDate], [TotalAmount], [DiscountAmount], [Status], [Note], [DeliveryAddress], [PromotionCode]) VALUES (4, NULL, CAST(N'2025-05-20T00:36:26.593' AS DateTime), CAST(200000.00 AS Decimal(10, 2)), CAST(0.00 AS Decimal(10, 2)), N'Pending', N'string', N'string', N'')
GO
INSERT [dbo].[Orders] ([OrderId], [UserId], [OrderDate], [TotalAmount], [DiscountAmount], [Status], [Note], [DeliveryAddress], [PromotionCode]) VALUES (5, 1, CAST(N'2025-05-20T00:40:20.827' AS DateTime), CAST(200000.00 AS Decimal(10, 2)), CAST(0.00 AS Decimal(10, 2)), N'Pending', N'string', N'string', N'')
GO
INSERT [dbo].[Orders] ([OrderId], [UserId], [OrderDate], [TotalAmount], [DiscountAmount], [Status], [Note], [DeliveryAddress], [PromotionCode]) VALUES (6, 1, CAST(N'2025-05-20T00:50:15.917' AS DateTime), CAST(200000.00 AS Decimal(10, 2)), CAST(0.00 AS Decimal(10, 2)), N'Pending', N'string', N'string', N'')
GO
INSERT [dbo].[Orders] ([OrderId], [UserId], [OrderDate], [TotalAmount], [DiscountAmount], [Status], [Note], [DeliveryAddress], [PromotionCode]) VALUES (7, 1, CAST(N'2025-05-20T01:03:53.073' AS DateTime), CAST(200000.00 AS Decimal(10, 2)), CAST(0.00 AS Decimal(10, 2)), N'Pending', N'string', N'string', N'')
GO
INSERT [dbo].[Orders] ([OrderId], [UserId], [OrderDate], [TotalAmount], [DiscountAmount], [Status], [Note], [DeliveryAddress], [PromotionCode]) VALUES (8, 1, CAST(N'2025-05-20T01:19:33.680' AS DateTime), CAST(200000.00 AS Decimal(10, 2)), CAST(0.00 AS Decimal(10, 2)), N'Complete', N'string', N'string', N'')
GO
INSERT [dbo].[Orders] ([OrderId], [UserId], [OrderDate], [TotalAmount], [DiscountAmount], [Status], [Note], [DeliveryAddress], [PromotionCode]) VALUES (9, 1, CAST(N'2025-05-19T18:27:37.720' AS DateTime), CAST(300000.00 AS Decimal(10, 2)), CAST(0.00 AS Decimal(10, 2)), N'Pending', N'string', N'string', N'')
GO
INSERT [dbo].[Orders] ([OrderId], [UserId], [OrderDate], [TotalAmount], [DiscountAmount], [Status], [Note], [DeliveryAddress], [PromotionCode]) VALUES (10, 1, CAST(N'2025-05-19T18:29:10.557' AS DateTime), CAST(300000.00 AS Decimal(10, 2)), CAST(0.00 AS Decimal(10, 2)), N'Pending', N'string', N'string', N'')
GO
INSERT [dbo].[Orders] ([OrderId], [UserId], [OrderDate], [TotalAmount], [DiscountAmount], [Status], [Note], [DeliveryAddress], [PromotionCode]) VALUES (11, 1, CAST(N'2025-05-19T18:33:10.500' AS DateTime), CAST(300000.00 AS Decimal(10, 2)), CAST(0.00 AS Decimal(10, 2)), N'Complete', N'string', N'string', N'')
GO
INSERT [dbo].[Orders] ([OrderId], [UserId], [OrderDate], [TotalAmount], [DiscountAmount], [Status], [Note], [DeliveryAddress], [PromotionCode]) VALUES (12, 1, CAST(N'2025-05-20T04:49:56.907' AS DateTime), CAST(300000.00 AS Decimal(10, 2)), CAST(0.00 AS Decimal(10, 2)), N'Pending', N'string', N'string', N'')
GO
INSERT [dbo].[Orders] ([OrderId], [UserId], [OrderDate], [TotalAmount], [DiscountAmount], [Status], [Note], [DeliveryAddress], [PromotionCode]) VALUES (13, 1, CAST(N'2025-05-20T12:02:11.393' AS DateTime), CAST(500000.00 AS Decimal(10, 2)), CAST(0.00 AS Decimal(10, 2)), N'Pending', N'string', N'string', N'')
GO
INSERT [dbo].[Orders] ([OrderId], [UserId], [OrderDate], [TotalAmount], [DiscountAmount], [Status], [Note], [DeliveryAddress], [PromotionCode]) VALUES (14, 1, CAST(N'2025-05-20T12:24:09.453' AS DateTime), CAST(500000.00 AS Decimal(10, 2)), CAST(0.00 AS Decimal(10, 2)), N'Cancel', N'string', N'string', N'')
GO
INSERT [dbo].[Orders] ([OrderId], [UserId], [OrderDate], [TotalAmount], [DiscountAmount], [Status], [Note], [DeliveryAddress], [PromotionCode]) VALUES (15, 1, CAST(N'2025-05-20T12:24:34.983' AS DateTime), CAST(500000.00 AS Decimal(10, 2)), CAST(0.00 AS Decimal(10, 2)), N'Complete', N'string', N'string', N'')
GO
INSERT [dbo].[Orders] ([OrderId], [UserId], [OrderDate], [TotalAmount], [DiscountAmount], [Status], [Note], [DeliveryAddress], [PromotionCode]) VALUES (16, 1, CAST(N'2025-05-20T12:26:05.260' AS DateTime), CAST(500000.00 AS Decimal(10, 2)), CAST(0.00 AS Decimal(10, 2)), N'Pending', N'string', N'string', N'')
GO
SET IDENTITY_INSERT [dbo].[Orders] OFF
GO
-- Chèn dữ liệu vào bảng Payments nếu bảng tồn tại và có đủ cột
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Payments]') AND type in (N'U'))
AND EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Payments]') AND name = 'OrderId')
AND EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Payments]') AND name = 'PaidAmount')
AND EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Payments]') AND name = 'Status')
BEGIN
    SET IDENTITY_INSERT [dbo].[Payments] ON;

    INSERT [dbo].[Payments] ([PaymentId], [OrderId], [PaymentMethod], [PaidAmount], [PaymentDate], [Status]) VALUES (1, 8, N'VNPay', CAST(200000.00 AS Decimal(10, 2)), CAST(N'2025-05-20T01:20:16.397' AS DateTime), N'Paid');
    INSERT [dbo].[Payments] ([PaymentId], [OrderId], [PaymentMethod], [PaidAmount], [PaymentDate], [Status]) VALUES (2, 11, N'VNPay', CAST(300000.00 AS Decimal(10, 2)), CAST(N'2025-05-19T18:33:43.573' AS DateTime), N'Paid');
    INSERT [dbo].[Payments] ([PaymentId], [OrderId], [PaymentMethod], [PaidAmount], [PaymentDate], [Status]) VALUES (3, 13, N'VNPay', CAST(500000.00 AS Decimal(10, 2)), CAST(N'2025-05-20T12:02:25.450' AS DateTime), N'Cancel');
    INSERT [dbo].[Payments] ([PaymentId], [OrderId], [PaymentMethod], [PaidAmount], [PaymentDate], [Status]) VALUES (4, 14, N'VNPay', CAST(500000.00 AS Decimal(10, 2)), CAST(N'2025-05-20T12:24:21.243' AS DateTime), N'Cancel');
    INSERT [dbo].[Payments] ([PaymentId], [OrderId], [PaymentMethod], [PaidAmount], [PaymentDate], [Status]) VALUES (5, 15, N'VNPay', CAST(500000.00 AS Decimal(10, 2)), CAST(N'2025-05-20T12:25:23.997' AS DateTime), N'Paid');

    SET IDENTITY_INSERT [dbo].[Payments] OFF;
END
GO
SET IDENTITY_INSERT [dbo].[Products] ON
GO
INSERT [dbo].[Products] ([ProductId], [Name], [Description], [Price], [ImageUrl], [CategoryId], [IsAvailable], [CreatedAt]) VALUES (1, N'Nước Chanh Leo', N'Một Loại Nước chua chua, ngọt ngọt, phù hợp với mùa hè nóng nực', CAST(10000.00 AS Decimal(10, 2)), N'string', 1, 1, CAST(N'2025-05-17T09:53:24.970' AS DateTime))
GO
INSERT [dbo].[Products] ([ProductId], [Name], [Description], [Price], [ImageUrl], [CategoryId], [IsAvailable], [CreatedAt]) VALUES (2, N'Nước Cam', N'Loại nước chứa nhiều vitamin C', CAST(10000.00 AS Decimal(10, 2)), N'https://suckhoedoisong.qltns.mediacdn.vn/324455921873985536/2023/11/7/uong-nuoc-cam-16993504421751885406385.jpg', 1, 1, CAST(N'2025-05-17T21:38:01.897' AS DateTime))
GO
SET IDENTITY_INSERT [dbo].[Products] OFF
GO
SET IDENTITY_INSERT [dbo].[Roles] ON
GO
INSERT [dbo].[Roles] ([RoleId], [RoleName]) VALUES (1, N'User')
GO
INSERT [dbo].[Roles] ([RoleId], [RoleName]) VALUES (2, N'Staff')
GO
INSERT [dbo].[Roles] ([RoleId], [RoleName]) VALUES (3, N'Guest')
GO
INSERT [dbo].[Roles] ([RoleId], [RoleName]) VALUES (4, N'Admin')
GO
SET IDENTITY_INSERT [dbo].[Roles] OFF
GO
-- Chèn dữ liệu vào bảng Users nếu bảng tồn tại và có đủ cột
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Users]') AND type in (N'U'))
AND EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Users]') AND name = 'PasswordHash')
AND EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Users]') AND name = 'Address')
AND EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Users]') AND name = 'Gender')
AND EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Users]') AND name = 'BirthDate')
AND EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Users]') AND name = 'RoleId')
AND EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Users]') AND name = 'CreatedAt')
AND EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Users]') AND name = 'RefreshToken')
AND EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Users]') AND name = 'RefreshTokenExpiryTime')
BEGIN
    SET IDENTITY_INSERT [dbo].[Users] ON;

    INSERT [dbo].[Users] ([UserId], [FullName], [Email], [PasswordHash], [Phone], [Address], [Gender], [BirthDate], [RoleId], [CreatedAt], [RefreshToken], [RefreshTokenExpiryTime]) VALUES (1, N'Quang Đỗ', N'<EMAIL>', N'$2a$11$AVG1/BmKnaQym2y6XVLj2.Kax4hOLa9Xh6/NiReiVvLkGRWOMshjS', N'0366152345', N'Hồ Chí Minh', N'Male', CAST(N'2005-03-18' AS Date), 1, CAST(N'2025-05-17T09:49:44.333' AS DateTime), N'o/oN85J4KK6NhONr2m2LDTio3+RwkBj/1A9MGo7Hzs0=', CAST(N'2025-05-27T06:19:10.767' AS DateTime));
    INSERT [dbo].[Users] ([UserId], [FullName], [Email], [PasswordHash], [Phone], [Address], [Gender], [BirthDate], [RoleId], [CreatedAt], [RefreshToken], [RefreshTokenExpiryTime]) VALUES (2, N'Đỗ', N'<EMAIL>', N'$2a$11$Z9ZKeFIY1SQPtM2TBK65IOJuJGLB11IM4V226IgN2Fc4wljef8xY2', N'0366501742', N'Xóm 9 Thôn Phú Long', N'Female', CAST(N'2005-05-17' AS Date), 2, CAST(N'2025-05-17T09:51:12.460' AS DateTime), N'Jup/CHokhS8+DH1rSgC7b+kUp6NNykraa6toBimyx9I=', CAST(N'2025-05-24T14:36:52.447' AS DateTime));
    INSERT [dbo].[Users] ([UserId], [FullName], [Email], [PasswordHash], [Phone], [Address], [Gender], [BirthDate], [RoleId], [CreatedAt], [RefreshToken], [RefreshTokenExpiryTime]) VALUES (3, N'Admin', N'<EMAIL>', N'$2a$11$0vnR/Qv24P8TUzbDo6hFFuD5/DVhJt4ZVqLONVlaZjXWQUIWMeMJ2', N'0123456789', N'string', N'Male', CAST(N'2005-05-20' AS Date), 4, CAST(N'2025-05-20T13:14:47.170' AS DateTime), NULL, NULL);

    SET IDENTITY_INSERT [dbo].[Users] OFF;
END
GO
ALTER TABLE [dbo].[CartItems]  WITH CHECK ADD  CONSTRAINT [FK_CartItems_Products] FOREIGN KEY([ProductId])
REFERENCES [dbo].[Products] ([ProductId])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[CartItems] CHECK CONSTRAINT [FK_CartItems_Products]
GO
ALTER TABLE [dbo].[CartItems]  WITH CHECK ADD  CONSTRAINT [FK_CartItems_Users] FOREIGN KEY([UserId])
REFERENCES [dbo].[Users] ([UserId])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[CartItems] CHECK CONSTRAINT [FK_CartItems_Users]
GO
ALTER TABLE [dbo].[WorkshopTickets]  WITH CHECK ADD FOREIGN KEY([WorkshopId])
REFERENCES [dbo].[Workshops] ([WorkshopId])
GO
ALTER TABLE [dbo].[CartItems]  WITH CHECK ADD CHECK  (([Quantity]>(0)))
GO

-- Script hoàn thành
PRINT 'Database script executed successfully!'
GO
