import React, { useState, useEffect } from "react";
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Alert,
  CircularProgress,
  Chip,
  Avatar,
  Card,
  CardContent,
  Stack,
  Divider,
  Grid,
  InputAdornment,
} from "@mui/material";
import {
  Add,
  Edit,
  Delete,
  Visibility,
  VisibilityOff,
  Search,
  FilterList,
  Inventory,
  AttachMoney,
  Image,
  Category as CategoryIcon,
  Close,
} from "@mui/icons-material";
import { useAppSelector } from "../../../stores/hooks";
import { productsAPI, categoriesAPI } from "../../../services/api";
import { Product, Category } from "../../../types";
import { toast } from "react-toastify";
import { useNavigate } from "react-router-dom";
import AuthDebug from "../../../components/AuthDebug";

const AdminProducts = () => {
  const { user, isAuthenticated } = useAppSelector((state) => state.Authentication);
  const navigate = useNavigate();

  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [error, setError] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [filterCategory, setFilterCategory] = useState<number>(0);

  const [formData, setFormData] = useState({
    name: "",
    description: "",
    price: 0,
    imageUrl: "",
    categoryId: 0,
    isAvailable: true,
  });

  useEffect(() => {
    console.log('AdminProducts useEffect - Auth state:', {
      isAuthenticated,
      user,
      userRoleId: user?.roleId,
      hasCorrectRole: user?.roleId === 4
    });

    if (!isAuthenticated || user?.roleId !== 4) {
      console.log('AdminProducts - Access denied, redirecting to home');
      navigate("/");
      return;
    }

    console.log('AdminProducts - Access granted, loading data');
    loadProducts();
    loadCategories();
  }, [isAuthenticated, user, navigate]);

  const loadProducts = async () => {
    try {
      setLoading(true);
      console.log('Loading products for admin...');
      const data = await productsAPI.getAll();
      console.log('Admin products loaded:', data);

      if (Array.isArray(data)) {
        setProducts(data);
      } else {
        console.error('Products data is not an array:', data);
        setProducts([]);
        toast.error("Dữ liệu sản phẩm không hợp lệ");
      }
    } catch (error: any) {
      console.error('Error loading products for admin:', error);
      toast.error(`Không thể tải danh sách sản phẩm: ${error.message}`);
      setProducts([]);
    } finally {
      setLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      console.log('Loading categories for admin...');
      const data = await categoriesAPI.getAll();
      console.log('Admin categories loaded:', data);

      if (Array.isArray(data)) {
        setCategories(data);
      } else {
        console.error('Categories data is not an array:', data);
        setCategories([]);
        toast.error("Dữ liệu danh mục không hợp lệ");
      }
    } catch (error: any) {
      console.error('Error loading categories for admin:', error);
      toast.error(`Không thể tải danh sách danh mục: ${error.message}`);
      setCategories([]);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleOpenDialog = (product?: Product) => {
    if (product) {
      setEditingProduct(product);
      setFormData({
        name: product.name,
        description: product.description,
        price: product.price,
        imageUrl: product.imageUrl,
        categoryId: product.categoryId,
        isAvailable: product.isAvailable,
      });
    } else {
      setEditingProduct(null);
      setFormData({
        name: "",
        description: "",
        price: 0,
        imageUrl: "",
        categoryId: 0,
        isAvailable: true,
      });
    }
    setDialogOpen(true);
    setError("");
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingProduct(null);
    setError("");
  };

  const handleSubmit = async () => {
    setError("");

    if (!formData.name || !formData.description || formData.price <= 0 || !formData.imageUrl || formData.categoryId === 0) {
      setError("Vui lòng điền đầy đủ thông tin");
      return;
    }

    try {
      setLoading(true);

      if (editingProduct) {
        console.log('Updating product:', editingProduct.productId, formData);
        await productsAPI.update(editingProduct.productId, formData);
        toast.success("Cập nhật sản phẩm thành công!");
      } else {
        console.log('Creating product:', formData);
        await productsAPI.create(formData);
        toast.success("Thêm sản phẩm thành công!");
      }

      handleCloseDialog();
      loadProducts();
    } catch (error: any) {
      console.error('Error saving product:', error);
      setError(`Có lỗi xảy ra khi lưu sản phẩm: ${error.message}`);
      toast.error(`Lưu sản phẩm thất bại: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (productId: number) => {
    if (!window.confirm("Bạn có chắc chắn muốn xóa sản phẩm này?")) {
      return;
    }

    try {
      await productsAPI.delete(productId);
      toast.success("Xóa sản phẩm thành công!");
      loadProducts();
    } catch (error) {
      toast.error("Không thể xóa sản phẩm");
    }
  };

  const getCategoryName = (categoryId: number) => {
    const category = categories.find(c => c.categoryId === categoryId);
    return category?.name || "Không xác định";
  };

  // Filter products based on search term and category
  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = filterCategory === 0 || product.categoryId === filterCategory;
    return matchesSearch && matchesCategory;
  });

  // Statistics
  const totalProducts = products.length;
  const availableProducts = products.filter(p => p.isAvailable).length;
  const totalValue = products.reduce((sum, p) => sum + p.price, 0);

  console.log('AdminProducts render - Auth state:', {
    isAuthenticated,
    user,
    userRoleId: user?.roleId,
    hasCorrectRole: user?.roleId === 4
  });

  if (!isAuthenticated || user?.roleId !== 4) {
    console.log('AdminProducts render - Showing access denied message');
    return (
      <Container maxWidth="md" sx={{ mt: 4 }}>
        <Alert severity="error">
          Bạn không có quyền truy cập trang này
          <br />
          Debug: isAuthenticated={String(isAuthenticated)}, userRoleId={user?.roleId}
        </Alert>
      </Container>
    );
  }

  console.log('AdminProducts render - Showing admin interface');

  return (
    <Box>
      {/* Debug Info */}
      <AuthDebug />

      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" sx={{ fontWeight: 700, color: '#1a1a2e', mb: 1 }}>
          📦 Quản lý sản phẩm
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Quản lý toàn bộ sản phẩm trong hệ thống
        </Typography>
      </Box>

      {/* Statistics Cards */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            borderRadius: 3
          }}>
            <CardContent>
              <Stack direction="row" alignItems="center" spacing={2}>
                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 48, height: 48 }}>
                  <Inventory />
                </Avatar>
                <Box>
                  <Typography variant="h5" sx={{ fontWeight: 700 }}>
                    {totalProducts}
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.9 }}>
                    Tổng sản phẩm
                  </Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{
            background: 'linear-gradient(135deg, #4caf50 0%, #45a049 100%)',
            color: 'white',
            borderRadius: 3
          }}>
            <CardContent>
              <Stack direction="row" alignItems="center" spacing={2}>
                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 48, height: 48 }}>
                  <Visibility />
                </Avatar>
                <Box>
                  <Typography variant="h5" sx={{ fontWeight: 700 }}>
                    {availableProducts}
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.9 }}>
                    Có sẵn
                  </Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{
            background: 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)',
            color: 'white',
            borderRadius: 3
          }}>
            <CardContent>
              <Stack direction="row" alignItems="center" spacing={2}>
                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 48, height: 48 }}>
                  <VisibilityOff />
                </Avatar>
                <Box>
                  <Typography variant="h5" sx={{ fontWeight: 700 }}>
                    {totalProducts - availableProducts}
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.9 }}>
                    Hết hàng
                  </Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{
            background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
            color: 'white',
            borderRadius: 3
          }}>
            <CardContent>
              <Stack direction="row" alignItems="center" spacing={2}>
                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 48, height: 48 }}>
                  <AttachMoney />
                </Avatar>
                <Box>
                  <Typography variant="h5" sx={{ fontWeight: 700 }}>
                    {Math.round(totalValue / 1000000)}M
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.9 }}>
                    Tổng giá trị
                  </Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Controls */}
      <Card sx={{ borderRadius: 3, mb: 3 }}>
        <CardContent>
          <Stack direction={{ xs: 'column', md: 'row' }} spacing={2} alignItems="center">
            <TextField
              placeholder="Tìm kiếm sản phẩm..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search color="action" />
                  </InputAdornment>
                ),
              }}
              sx={{ flex: 1, minWidth: 300 }}
            />

            <FormControl sx={{ minWidth: 200 }}>
              <InputLabel>Lọc theo danh mục</InputLabel>
              <Select
                value={filterCategory}
                label="Lọc theo danh mục"
                onChange={(e) => setFilterCategory(Number(e.target.value))}
                startAdornment={<FilterList sx={{ mr: 1, color: 'action.active' }} />}
              >
                <MenuItem value={0}>Tất cả danh mục</MenuItem>
                {categories.map((category) => (
                  <MenuItem key={category.categoryId} value={category.categoryId}>
                    {category.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => handleOpenDialog()}
              sx={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                borderRadius: 3,
                px: 3,
                py: 1.5,
                boxShadow: '0 4px 16px rgba(102, 126, 234, 0.3)',
                '&:hover': {
                  background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
                  boxShadow: '0 6px 20px rgba(102, 126, 234, 0.4)',
                  transform: 'translateY(-2px)'
                },
                transition: 'all 0.3s ease'
              }}
            >
              Thêm sản phẩm
            </Button>
          </Stack>
        </CardContent>
      </Card>

      {loading && (
        <Box display="flex" justifyContent="center" my={4}>
          <CircularProgress />
        </Box>
      )}

      {/* Products Table */}
      <Card sx={{ borderRadius: 3, overflow: 'hidden' }}>
        <Box sx={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          p: 3
        }}>
          <Stack direction="row" justifyContent="space-between" alignItems="center">
            <Box>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                📋 Danh sách sản phẩm
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.9 }}>
                Hiển thị {filteredProducts.length} / {totalProducts} sản phẩm
              </Typography>
            </Box>
          </Stack>
        </Box>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow sx={{ bgcolor: 'rgba(102, 126, 234, 0.05)' }}>
                <TableCell sx={{ fontWeight: 600, color: '#667eea' }}>Hình ảnh</TableCell>
                <TableCell sx={{ fontWeight: 600, color: '#667eea' }}>Tên sản phẩm</TableCell>
                <TableCell sx={{ fontWeight: 600, color: '#667eea' }}>Mô tả</TableCell>
                <TableCell sx={{ fontWeight: 600, color: '#667eea' }}>Giá</TableCell>
                <TableCell sx={{ fontWeight: 600, color: '#667eea' }}>Danh mục</TableCell>
                <TableCell sx={{ fontWeight: 600, color: '#667eea' }}>Trạng thái</TableCell>
                <TableCell align="center" sx={{ fontWeight: 600, color: '#667eea' }}>Thao tác</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredProducts.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} align="center" sx={{ py: 4 }}>
                    <Typography variant="body1" color="text.secondary">
                      {searchTerm || filterCategory ? 'Không tìm thấy sản phẩm phù hợp' : 'Chưa có sản phẩm nào'}
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                filteredProducts.map((product) => (
                  <TableRow
                    key={product.productId}
                    sx={{
                      '&:hover': { bgcolor: 'rgba(102, 126, 234, 0.02)' },
                      transition: 'background-color 0.2s ease'
                    }}
                  >
                    <TableCell>
                      <Avatar
                        src={product.imageUrl}
                        alt={product.name}
                        variant="rounded"
                        sx={{
                          width: 64,
                          height: 64,
                          border: '2px solid rgba(102, 126, 234, 0.1)',
                          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                        }}
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 0.5 }}>
                        {product.name}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        ID: {product.productId}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography
                        variant="body2"
                        sx={{
                          maxWidth: 200,
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap'
                        }}
                        title={product.description}
                      >
                        {product.description}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" sx={{
                        fontWeight: 600,
                        color: '#4caf50',
                        fontSize: '1rem'
                      }}>
                        {product.price.toLocaleString("vi-VN")}đ
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={getCategoryName(product.categoryId)}
                        size="small"
                        sx={{
                          bgcolor: 'rgba(102, 126, 234, 0.1)',
                          color: '#667eea',
                          fontWeight: 500
                        }}
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={product.isAvailable ? "Có sẵn" : "Hết hàng"}
                        color={product.isAvailable ? "success" : "error"}
                        size="small"
                        sx={{ fontWeight: 500 }}
                      />
                    </TableCell>
                    <TableCell align="center">
                      <Stack direction="row" spacing={1} justifyContent="center">
                        <IconButton
                          size="small"
                          onClick={() => handleOpenDialog(product)}
                          sx={{
                            bgcolor: 'rgba(102, 126, 234, 0.1)',
                            color: '#667eea',
                            '&:hover': {
                              bgcolor: 'rgba(102, 126, 234, 0.2)',
                              transform: 'scale(1.1)'
                            },
                            transition: 'all 0.2s ease'
                          }}
                        >
                          <Edit fontSize="small" />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleDelete(product.productId)}
                          sx={{
                            bgcolor: 'rgba(244, 67, 54, 0.1)',
                            color: '#f44336',
                            '&:hover': {
                              bgcolor: 'rgba(244, 67, 54, 0.2)',
                              transform: 'scale(1.1)'
                            },
                            transition: 'all 0.2s ease'
                          }}
                        >
                          <Delete fontSize="small" />
                        </IconButton>
                      </Stack>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Card>

      {/* Add/Edit Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 4,
            overflow: 'hidden',
          }
        }}
      >
        {/* Dialog Header */}
        <Box sx={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          p: 3,
          position: 'relative'
        }}>
          <IconButton
            onClick={handleCloseDialog}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
              color: 'white',
              bgcolor: 'rgba(255,255,255,0.1)',
              '&:hover': { bgcolor: 'rgba(255,255,255,0.2)' }
            }}
          >
            <Close />
          </IconButton>

          <Stack direction="row" spacing={2} alignItems="center">
            <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
              {editingProduct ? <Edit /> : <Add />}
            </Avatar>
            <Box>
              <Typography variant="h5" sx={{ fontWeight: 600, mb: 1 }}>
                {editingProduct ? "✏️ Chỉnh sửa sản phẩm" : "➕ Thêm sản phẩm mới"}
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.9 }}>
                {editingProduct ? "Cập nhật thông tin sản phẩm" : "Điền thông tin sản phẩm mới"}
              </Typography>
            </Box>
          </Stack>
        </Box>

        <DialogContent sx={{ p: 4 }}>
          {error && (
            <Alert severity="error" sx={{ mb: 3, borderRadius: 2 }}>
              {error}
            </Alert>
          )}

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Tên sản phẩm"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                required
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Inventory color="action" />
                    </InputAdornment>
                  ),
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '&.Mui-focused fieldset': { borderColor: '#667eea' }
                  }
                }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Giá (VNĐ)"
                type="number"
                value={formData.price}
                onChange={(e) => handleInputChange("price", Number(e.target.value))}
                required
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <AttachMoney color="action" />
                    </InputAdornment>
                  ),
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '&.Mui-focused fieldset': { borderColor: '#667eea' }
                  }
                }}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Mô tả sản phẩm"
                value={formData.description}
                onChange={(e) => handleInputChange("description", e.target.value)}
                multiline
                rows={3}
                required
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '&.Mui-focused fieldset': { borderColor: '#667eea' }
                  }
                }}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="URL hình ảnh"
                value={formData.imageUrl}
                onChange={(e) => handleInputChange("imageUrl", e.target.value)}
                required
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Image color="action" />
                    </InputAdornment>
                  ),
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '&.Mui-focused fieldset': { borderColor: '#667eea' }
                  }
                }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth required>
                <InputLabel>Danh mục</InputLabel>
                <Select
                  value={formData.categoryId}
                  label="Danh mục"
                  onChange={(e) => handleInputChange("categoryId", e.target.value)}
                  startAdornment={<Category sx={{ mr: 1, color: 'action.active' }} />}
                  sx={{
                    borderRadius: 2,
                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': { borderColor: '#667eea' }
                  }}
                >
                  {categories.map((category) => (
                    <MenuItem key={category.categoryId} value={category.categoryId}>
                      {category.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <Box sx={{
                p: 2,
                border: '1px solid rgba(0,0,0,0.12)',
                borderRadius: 2,
                bgcolor: 'rgba(102, 126, 234, 0.02)'
              }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.isAvailable}
                      onChange={(e) => handleInputChange("isAvailable", e.target.checked)}
                      sx={{
                        '& .MuiSwitch-switchBase.Mui-checked': {
                          color: '#667eea',
                        },
                        '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                          backgroundColor: '#667eea',
                        },
                      }}
                    />
                  }
                  label={
                    <Box>
                      <Typography variant="body2" fontWeight={600}>
                        Trạng thái sản phẩm
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {formData.isAvailable ? 'Có sẵn' : 'Hết hàng'}
                      </Typography>
                    </Box>
                  }
                />
              </Box>
            </Grid>

            {/* Preview Image */}
            {formData.imageUrl && (
              <Grid item xs={12}>
                <Box sx={{
                  p: 2,
                  border: '1px solid rgba(0,0,0,0.12)',
                  borderRadius: 2,
                  bgcolor: 'rgba(102, 126, 234, 0.02)'
                }}>
                  <Typography variant="body2" fontWeight={600} mb={2}>
                    Xem trước hình ảnh:
                  </Typography>
                  <Avatar
                    src={formData.imageUrl}
                    variant="rounded"
                    sx={{
                      width: 120,
                      height: 120,
                      border: '2px solid rgba(102, 126, 234, 0.2)',
                      boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
                    }}
                  />
                </Box>
              </Grid>
            )}
          </Grid>
        </DialogContent>

        <DialogActions sx={{ p: 4, pt: 0 }}>
          <Stack direction="row" spacing={2} width="100%">
            <Button
              onClick={handleCloseDialog}
              variant="outlined"
              size="large"
              sx={{
                flex: 1,
                py: 1.5,
                borderColor: '#667eea',
                color: '#667eea',
                borderRadius: 3,
                fontWeight: 600,
                '&:hover': {
                  borderColor: '#5a6fd8',
                  bgcolor: 'rgba(102, 126, 234, 0.05)'
                }
              }}
            >
              Hủy bỏ
            </Button>
            <Button
              onClick={handleSubmit}
              variant="contained"
              size="large"
              disabled={loading}
              startIcon={loading ? <CircularProgress size={20} color="inherit" /> : (editingProduct ? <Edit /> : <Add />)}
              sx={{
                flex: 2,
                py: 1.5,
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                borderRadius: 3,
                fontWeight: 600,
                fontSize: '1rem',
                boxShadow: '0 4px 16px rgba(102, 126, 234, 0.3)',
                '&:hover': {
                  background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
                  boxShadow: '0 6px 20px rgba(102, 126, 234, 0.4)',
                  transform: 'translateY(-2px)'
                },
                '&:disabled': {
                  background: 'rgba(0,0,0,0.12)',
                  transform: 'none'
                },
                transition: 'all 0.3s ease'
              }}
            >
              {loading ? "Đang lưu..." : (editingProduct ? "Cập nhật sản phẩm" : "Thêm sản phẩm")}
            </Button>
          </Stack>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AdminProducts;
