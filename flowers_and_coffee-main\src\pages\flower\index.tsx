import React, { useState, useEffect } from "react";
import {
  Box,
  Typography,
  Card,
  CardMedia,
  CardContent,
  Button,
  Container,
  Pagination,
  CircularProgress,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Divider,
  Stack,
  Chip,
  Avatar,
} from "@mui/material";
import {
  Add,
  ShoppingCart,
  Remove,
  Close,
  LocalOffer
} from "@mui/icons-material";
import Grid from "@mui/material/Grid";
import { useNavigate } from "react-router-dom";
import bannerImage from "../../assets/banner_flower.png";
import "../styles/FontStyle.scss";
import { useAppDispatch, useAppSelector } from "../../stores/hooks";
import { setFlowerDetail } from "../../stores/reducers/Flower";
import { addCartItem, setCartItems } from "../../stores/reducers/Cart";
import { productsAPI, cartAPI } from "../../services/api";
import { Product } from "../../types";
import { toast } from "react-toastify";

const FlowerShopPage = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { isAuthenticated } = useAppSelector((state) => state.Authentication);

  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [quantity, setQuantity] = useState(1);

  useEffect(() => {
    loadProducts();
  }, []);

  const loadProducts = async () => {
    try {
      setLoading(true);
      console.log('Loading products...');
      const data = await productsAPI.getAll();
      console.log('Products loaded:', data);

      if (Array.isArray(data)) {
        const availableProducts = data.filter((product: Product) => product.isAvailable);
        console.log('Available products:', availableProducts);
        setProducts(availableProducts);
      } else {
        console.error('Products data is not an array:', data);
        setProducts([]);
        toast.error("Dữ liệu sản phẩm không hợp lệ");
      }
    } catch (error: any) {
      console.error('Error loading products:', error);
      toast.error(`Không thể tải danh sách sản phẩm: ${error.message}`);
      setProducts([]);
    } finally {
      setLoading(false);
    }
  };

  const handleClickFlower = (product: Product) => {
    dispatch(setFlowerDetail(product));
    navigate("/flower/detail");
  };

  const handleAddToCart = (product: Product) => {
    if (!isAuthenticated) {
      toast.warning("Vui lòng đăng nhập để thêm sản phẩm vào giỏ hàng");
      navigate("/auth/login");
      return;
    }

    setSelectedProduct(product);
    setQuantity(1);
    setDialogOpen(true);
  };

  const handleConfirmAddToCart = async () => {
    if (!selectedProduct) return;

    try {
      console.log('Adding product to cart:', selectedProduct.name, 'quantity:', quantity);
      await cartAPI.addItem(selectedProduct.productId, quantity);

      // Always reload cart from server to get accurate data
      const cartData = await cartAPI.getCart();
      console.log('Reloaded cart data:', cartData);
      dispatch(setCartItems(cartData));

      toast.success(`Đã thêm ${selectedProduct.name} vào giỏ hàng`);
      setDialogOpen(false);
    } catch (error: any) {
      console.error('Error adding to cart:', error);
      toast.error(`Không thể thêm sản phẩm vào giỏ hàng: ${error.message}`);
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box
        sx={{
          position: "relative",
          height: "500px",
          mb: 4,
          overflow: "hidden",
          borderRadius: 1,
        }}
      >
        <CardMedia
          component="img"
          height="500px"
          image={bannerImage}
          alt="Banner of flowers"
          sx={{
            objectFit: "cover",
            width: "100%",
          }}
        />
        <Typography
          variant="h1"
          component="h1"
          className="workshop-title"
          sx={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
          }}
        >
          Hoa
        </Typography>
      </Box>
      <Container maxWidth="lg" sx={{ mt: 10 }}>
        {loading ? (
          <Box display="flex" justifyContent="center" my={4}>
            <CircularProgress />
          </Box>
        ) : (
          <Grid container spacing={4}>
            {products.map((product) => (
              <Grid size={{ xs: 6, sm: 3 }} key={product.productId}>
                <Card
                  sx={{
                    height: "100%",
                    display: "flex",
                    flexDirection: "column",
                  }}
                >
                  <CardMedia
                    component="img"
                    height="200"
                    image={product.imageUrl}
                    alt={product.name}
                    sx={{ objectFit: "cover" }}
                  />
                  <CardContent
                    sx={{
                      p: 1,
                      pb: 0,
                      backgroundColor: "#9e655c",
                      color: "#fff",
                    }}
                  >
                    <Typography
                      variant="subtitle2"
                      component="div"
                      fontWeight="bold"
                      sx={{ mb: 0 }}
                    >
                      {product.name}
                    </Typography>
                    <Typography
                      variant="body2"
                      color="#fff"
                      sx={{ mb: 1, fontSize: "0.75rem" }}
                    >
                      giá: {product.price.toLocaleString("vi-VN")}đ
                    </Typography>
                  </CardContent>
                  <Box
                    sx={{
                      p: 1,
                      pt: 0,
                      mt: "auto",
                      backgroundColor: "#9e655c",
                      display: "flex",
                      gap: 1,
                    }}
                  >
                    <Button
                      onClick={() => handleClickFlower(product)}
                      variant="contained"
                      size="small"
                      sx={{
                        backgroundColor: "#E8A295",
                        "&:hover": { backgroundColor: "#c6948c" },
                        textTransform: "none",
                        borderRadius: 1,
                        py: 1,
                        flex: 1,
                      }}
                    >
                      Xem chi tiết
                    </Button>
                    <IconButton
                      onClick={() => handleAddToCart(product)}
                      size="small"
                      sx={{
                        backgroundColor: "#E8A295",
                        color: "white",
                        "&:hover": { backgroundColor: "#c6948c" },
                      }}
                    >
                      <ShoppingCart fontSize="small" />
                    </IconButton>
                  </Box>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
        <Box sx={{ display: "flex", justifyContent: "center", mt: 4 }}>
          <Pagination
            count={12}
            variant="outlined"
            shape="rounded"
            defaultPage={8}
            siblingCount={1}
            size="small"
          />
        </Box>
      </Container>

      {/* Add to Cart Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 4,
            overflow: 'hidden',
            background: 'linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)',
          }
        }}
      >
        {selectedProduct && (
          <>
            {/* Header */}
            <Box sx={{
              background: 'linear-gradient(135deg, #9e655c 0%, #8b4a47 100%)',
              color: 'white',
              p: 3,
              position: 'relative'
            }}>
              <IconButton
                onClick={() => setDialogOpen(false)}
                sx={{
                  position: 'absolute',
                  right: 8,
                  top: 8,
                  color: 'white',
                  bgcolor: 'rgba(255,255,255,0.1)',
                  '&:hover': { bgcolor: 'rgba(255,255,255,0.2)' }
                }}
              >
                <Close />
              </IconButton>

              <Stack direction="row" spacing={3} alignItems="center">
                <Avatar
                  src={selectedProduct.imageUrl}
                  sx={{
                    width: 80,
                    height: 80,
                    border: '3px solid rgba(255,255,255,0.3)',
                    boxShadow: '0 4px 16px rgba(0,0,0,0.2)'
                  }}
                />
                <Box flex={1}>
                  <Typography variant="h5" sx={{ fontWeight: 600, mb: 1 }}>
                    {selectedProduct.name}
                  </Typography>
                  <Stack direction="row" spacing={1} alignItems="center">
                    <LocalOffer sx={{ fontSize: 20 }} />
                    <Typography variant="h6" sx={{ fontWeight: 500 }}>
                      {selectedProduct.price.toLocaleString("vi-VN")}đ
                    </Typography>
                  </Stack>
                  <Chip
                    label="Có sẵn"
                    size="small"
                    sx={{
                      mt: 1,
                      bgcolor: 'rgba(76, 175, 80, 0.2)',
                      color: 'white',
                      border: '1px solid rgba(76, 175, 80, 0.3)'
                    }}
                  />
                </Box>
              </Stack>
            </Box>

            {/* Content */}
            <DialogContent sx={{ p: 4 }}>
              <Box sx={{ mb: 4 }}>
                <Typography variant="h6" sx={{
                  color: '#9e655c',
                  fontWeight: 600,
                  mb: 3,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1
                }}>
                  <ShoppingCart />
                  Chọn số lượng
                </Typography>

                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: 2,
                  p: 3,
                  bgcolor: 'rgba(158, 101, 92, 0.05)',
                  borderRadius: 3,
                  border: '2px solid rgba(158, 101, 92, 0.1)'
                }}>
                  <IconButton
                    onClick={() => setQuantity(Math.max(1, quantity - 1))}
                    disabled={quantity <= 1}
                    sx={{
                      bgcolor: '#9e655c',
                      color: 'white',
                      width: 48,
                      height: 48,
                      '&:hover': { bgcolor: '#8b4a47' },
                      '&:disabled': { bgcolor: 'grey.300', color: 'grey.500' }
                    }}
                  >
                    <Remove />
                  </IconButton>

                  <TextField
                    value={quantity}
                    onChange={(e) => setQuantity(Math.max(1, parseInt(e.target.value) || 1))}
                    inputProps={{
                      min: 1,
                      style: {
                        textAlign: 'center',
                        fontSize: '1.5rem',
                        fontWeight: 600,
                        color: '#9e655c'
                      }
                    }}
                    sx={{
                      width: 100,
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                        bgcolor: 'white',
                        '& fieldset': { borderColor: 'rgba(158, 101, 92, 0.3)' },
                        '&:hover fieldset': { borderColor: '#9e655c' },
                        '&.Mui-focused fieldset': { borderColor: '#9e655c' }
                      }
                    }}
                    variant="outlined"
                    type="number"
                  />

                  <IconButton
                    onClick={() => setQuantity(quantity + 1)}
                    sx={{
                      bgcolor: '#9e655c',
                      color: 'white',
                      width: 48,
                      height: 48,
                      '&:hover': { bgcolor: '#8b4a47' }
                    }}
                  >
                    <Add />
                  </IconButton>
                </Box>
              </Box>

              <Divider sx={{ my: 3 }} />

              {/* Total */}
              <Box sx={{
                p: 3,
                bgcolor: 'rgba(158, 101, 92, 0.05)',
                borderRadius: 3,
                border: '1px solid rgba(158, 101, 92, 0.1)'
              }}>
                <Stack direction="row" justifyContent="space-between" alignItems="center">
                  <Typography variant="h6" sx={{ color: '#9e655c', fontWeight: 600 }}>
                    Tổng cộng:
                  </Typography>
                  <Typography variant="h5" sx={{
                    color: '#9e655c',
                    fontWeight: 700,
                    fontSize: '1.8rem'
                  }}>
                    {(selectedProduct.price * quantity).toLocaleString("vi-VN")}đ
                  </Typography>
                </Stack>
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  {selectedProduct.price.toLocaleString("vi-VN")}đ × {quantity} sản phẩm
                </Typography>
              </Box>
            </DialogContent>

            {/* Actions */}
            <DialogActions sx={{ p: 4, pt: 0 }}>
              <Stack direction="row" spacing={2} width="100%">
                <Button
                  onClick={() => setDialogOpen(false)}
                  variant="outlined"
                  size="large"
                  sx={{
                    flex: 1,
                    py: 1.5,
                    borderColor: '#9e655c',
                    color: '#9e655c',
                    borderRadius: 3,
                    fontWeight: 600,
                    '&:hover': {
                      borderColor: '#8b4a47',
                      bgcolor: 'rgba(158, 101, 92, 0.05)'
                    }
                  }}
                >
                  Hủy bỏ
                </Button>
                <Button
                  onClick={handleConfirmAddToCart}
                  variant="contained"
                  size="large"
                  startIcon={<ShoppingCart />}
                  sx={{
                    flex: 2,
                    py: 1.5,
                    bgcolor: '#9e655c',
                    borderRadius: 3,
                    fontWeight: 600,
                    fontSize: '1rem',
                    boxShadow: '0 4px 16px rgba(158, 101, 92, 0.3)',
                    '&:hover': {
                      bgcolor: '#8b4a47',
                      boxShadow: '0 6px 20px rgba(158, 101, 92, 0.4)',
                      transform: 'translateY(-2px)'
                    },
                    transition: 'all 0.3s ease'
                  }}
                >
                  Thêm vào giỏ hàng
                </Button>
              </Stack>
            </DialogActions>
          </>
        )}
      </Dialog>
    </Container>
  );
};

export default FlowerShopPage;
