// API Base URL - Use relative path for proxy
const API_BASE_URL = '/api';

// Helper function to get auth token
const getAuthToken = () => {
  const token = localStorage.getItem('token') || '';
  console.log('Getting token from localStorage:', token ? `${token.substring(0, 20)}...` : 'No token found');

  // Debug: Parse and log token payload
  if (token) {
    try {
      const tokenPayload = JSON.parse(atob(token.split('.')[1]));
      console.log('Token payload:', tokenPayload);
      console.log('User ID claim:', tokenPayload['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier']);
      console.log('Token expiry:', new Date(tokenPayload.exp * 1000));
    } catch (e) {
      console.error('Error parsing token:', e);
    }
  }

  return token;
};

// Helper function to make authenticated requests
const makeAuthenticatedRequest = async (url: string, options: RequestInit = {}) => {
  const token = getAuthToken();
  console.log('Token being sent:', token ? `${token.substring(0, 20)}...` : 'No token');

  const headers = {
    'Content-Type': 'application/json',
    ...(token && { 'Authorization': `Bearer ${token}` }),
    ...options.headers,
  };

  console.log('Request headers:', headers);

  const response = await fetch(`${API_BASE_URL}${url}`, {
    ...options,
    headers,
  });

  console.log('Response status:', response.status);

  if (!response.ok) {
    const errorText = await response.text();
    console.log('Error response:', errorText);
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return response.json();
};

// Auth API
export const authAPI = {
  login: async (email: string, password: string) => {
    console.log('Login attempt:', { email, password: '***' });

    const response = await fetch(`${API_BASE_URL}/Auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password }),
    });

    console.log('Login response status:', response.status);

    if (!response.ok) {
      // Try to get error message from response
      try {
        const errorData = await response.json();
        console.log('Login error data:', errorData);
        throw new Error(errorData.message || errorData.error || 'Login failed');
      } catch (parseError) {
        const errorText = await response.text();
        console.log('Login error text:', errorText);
        throw new Error('Login failed');
      }
    }

    const result = await response.json();
    console.log('Login success result:', result);
    return result;
  },

  register: async (userData: {
    fullName: string;
    email: string;
    password: string;
    phone: string;
    address: string;
    gender: string;
    birthDate: string;
  }) => {
    const response = await fetch(`${API_BASE_URL}/Auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userData),
    });

    if (!response.ok) {
      // Try to get error message from response
      try {
        const errorData = await response.json();
        throw new Error(errorData.error || errorData.message || 'Registration failed');
      } catch (parseError) {
        throw new Error('Registration failed');
      }
    }

    return response.json();
  },

  getProfile: async () => {
    return makeAuthenticatedRequest('/Auth/profile');
  },

  updateProfile: async (userData: {
    fullName?: string;
    phone?: string;
    address?: string;
    gender?: string;
    birthDate?: string;
    oldPassword?: string;
    newPassword?: string;
    confirmPassword?: string;
  }) => {
    const token = getAuthToken();
    console.log('Update profile data:', userData);

    const headers = {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` }),
    };

    const response = await fetch(`${API_BASE_URL}/Auth/update-profile`, {
      method: 'PUT',
      headers,
      body: JSON.stringify(userData),
    });

    console.log('Update profile response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.log('Update profile error:', errorText);

      try {
        const errorData = JSON.parse(errorText);
        throw new Error(errorData.message || errorData.error || 'Update failed');
      } catch (parseError) {
        throw new Error(errorText || 'Update failed');
      }
    }

    // Backend returns success message, not user data
    const result = await response.text();
    console.log('Update profile success:', result);
    return result;
  },

  logout: async () => {
    return makeAuthenticatedRequest('/Auth/logout', {
      method: 'POST',
    });
  },
};

// Products API
export const productsAPI = {
  getAll: async () => {
    console.log('Fetching products from:', `${API_BASE_URL}/Products`);

    const response = await fetch(`${API_BASE_URL}/Products`);
    console.log('Products response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.log('Products error response:', errorText);
      throw new Error(`Failed to fetch products: ${response.status}`);
    }

    const result = await response.json();
    console.log('Products response data:', result);

    // Handle different response structures
    if (Array.isArray(result)) {
      return result;
    } else if (result.$values && Array.isArray(result.$values)) {
      // Handle JSON.NET serialization format
      return result.$values;
    } else if (result.data && Array.isArray(result.data)) {
      return result.data;
    } else {
      console.warn('Unexpected products response structure:', result);
      return [];
    }
  },

  getById: async (id: number) => {
    console.log('Fetching product by ID:', id);

    const response = await fetch(`${API_BASE_URL}/Products/${id}`);
    console.log('Product by ID response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.log('Product by ID error response:', errorText);
      throw new Error(`Failed to fetch product: ${response.status}`);
    }

    const result = await response.json();
    console.log('Product by ID response data:', result);
    return result;
  },

  create: async (productData: {
    name: string;
    description: string;
    price: number;
    imageUrl: string;
    categoryId: number;
    isAvailable: boolean;
  }) => {
    return makeAuthenticatedRequest('/Products', {
      method: 'POST',
      body: JSON.stringify(productData),
    });
  },

  update: async (id: number, productData: {
    name: string;
    description: string;
    price: number;
    imageUrl: string;
    categoryId: number;
    isAvailable: boolean;
  }) => {
    console.log('Updating product API call:', { id, productData });
    console.log('Full API URL will be:', `${API_BASE_URL}/Products/${id}`);

    try {
      const token = getAuthToken();
      const headers = {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
      };

      const response = await fetch(`${API_BASE_URL}/Products/${id}`, {
        method: 'PUT',
        headers,
        body: JSON.stringify(productData),
      });

      console.log('Update product response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.log('Update product error response:', errorText);
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // For PUT requests that return 200 OK with no content, just return success
      console.log('Update product successful');
      return { success: true };
    } catch (error: any) {
      console.error('Update product error details:', {
        message: error.message,
        stack: error.stack,
        url: `${API_BASE_URL}/Products/${id}`,
        method: 'PUT',
        data: productData
      });
      throw error;
    }
  },

  delete: async (id: number) => {
    return makeAuthenticatedRequest(`/Products/${id}`, {
      method: 'DELETE',
    });
  },
};

// Cart API
export const cartAPI = {
  getCart: async () => {
    console.log('Fetching cart...');
    const result = await makeAuthenticatedRequest('/Cart');
    console.log('Cart response:', result);

    // Handle different response structures
    let cartItems = [];
    if (Array.isArray(result)) {
      cartItems = result;
    } else if (result.$values && Array.isArray(result.$values)) {
      cartItems = result.$values;
    } else if (result.data && Array.isArray(result.data)) {
      cartItems = result.data;
    } else {
      console.warn('Unexpected cart response structure:', result);
      return [];
    }

    // Fetch product details for each cart item
    const enrichedCartItems = await Promise.all(
      cartItems.map(async (item: any) => {
        try {
          console.log('Fetching product details for productId:', item.productId);
          const productResponse = await fetch(`${API_BASE_URL}/Products/${item.productId}`);

          if (productResponse.ok) {
            const productData = await productResponse.json();
            console.log('Product data for', item.productId, ':', productData);

            return {
              id: item.id,
              userId: item.userId || 0,
              productId: item.productId,
              quantity: item.quantity,
              product: {
                productId: productData.productId,
                name: productData.name || item.productName,
                price: productData.price || 0,
                imageUrl: productData.imageUrl || '',
                description: productData.description || '',
                categoryId: productData.categoryId || 0,
                isAvailable: productData.isAvailable !== false,
                createdAt: productData.createdAt || ''
              }
            };
          } else {
            console.warn('Failed to fetch product details for', item.productId);
            return {
              id: item.id,
              userId: item.userId || 0,
              productId: item.productId,
              quantity: item.quantity,
              product: {
                productId: item.productId,
                name: item.productName || 'Unknown Product',
                price: 0,
                imageUrl: '',
                description: '',
                categoryId: 0,
                isAvailable: true,
                createdAt: ''
              }
            };
          }
        } catch (error) {
          console.error('Error fetching product details for', item.productId, ':', error);
          return {
            id: item.id,
            userId: item.userId || 0,
            productId: item.productId,
            quantity: item.quantity,
            product: {
              productId: item.productId,
              name: item.productName || 'Unknown Product',
              price: 0,
              imageUrl: '',
              description: '',
              categoryId: 0,
              isAvailable: true,
              createdAt: ''
            }
          };
        }
      })
    );

    console.log('Enriched cart items:', enrichedCartItems);
    return enrichedCartItems;
  },

  addItem: async (productId: number, quantity: number) => {
    console.log('Adding item to cart:', { productId, quantity });
    const result = await makeAuthenticatedRequest('/Cart', {
      method: 'POST',
      body: JSON.stringify({ productId, quantity }),
    });
    console.log('Add to cart response:', result);
    return result;
  },

  updateItem: async (productId: number, quantity: number) => {
    console.log('Updating cart item:', { productId, quantity });

    // Backend AddOrUpdateCartItem adds quantity, so we need to:
    // 1. Remove the item first
    // 2. Add it with the new quantity
    try {
      // Remove first
      await makeAuthenticatedRequest(`/Cart/${productId}`, {
        method: 'DELETE',
      });

      // Then add with new quantity
      const result = await makeAuthenticatedRequest('/Cart', {
        method: 'POST',
        body: JSON.stringify({ productId, quantity }),
      });
      console.log('Update cart item response:', result);
      return result;
    } catch (error) {
      console.error('Error updating cart item:', error);
      throw error;
    }
  },

  removeItem: async (productId: number) => {
    console.log('Removing item from cart:', productId);
    const result = await makeAuthenticatedRequest(`/Cart/${productId}`, {
      method: 'DELETE',
    });
    console.log('Remove from cart response:', result);
    return result;
  },
};

// Orders API
export const ordersAPI = {
  createFromCart: async (orderData: {
    note: string;
    deliveryAddress: string;
    promotionCode?: string;
    selectedCartItemIds?: number[];
  }) => {
    console.log('Creating order from cart:', orderData);

    // If no selectedCartItemIds provided, get all cart items
    if (!orderData.selectedCartItemIds || orderData.selectedCartItemIds.length === 0) {
      console.log('No selectedCartItemIds provided, fetching raw cart items...');
      try {
        // Get raw cart data from backend (not enriched)
        const rawCartData = await makeAuthenticatedRequest('/Cart');
        console.log('Raw cart data for order:', rawCartData);

        // Handle different response structures
        let cartItems = [];
        if (Array.isArray(rawCartData)) {
          cartItems = rawCartData;
        } else if (rawCartData.$values && Array.isArray(rawCartData.$values)) {
          cartItems = rawCartData.$values;
        } else if (rawCartData.data && Array.isArray(rawCartData.data)) {
          cartItems = rawCartData.data;
        }

        // Extract cart item IDs from backend response
        orderData.selectedCartItemIds = cartItems.map((item: any) => item.id);
        console.log('Selected cart item IDs:', orderData.selectedCartItemIds);

        if (orderData.selectedCartItemIds.length === 0) {
          throw new Error('Giỏ hàng trống');
        }
      } catch (error) {
        console.error('Error fetching cart items for order:', error);
        throw new Error('Không thể lấy thông tin giỏ hàng để tạo đơn hàng');
      }
    }

    const result = await makeAuthenticatedRequest('/Orders/from-cart', {
      method: 'POST',
      body: JSON.stringify(orderData),
    });
    console.log('Create order response:', result);
    return result;
  },

  getOrders: async () => {
    console.log('Fetching orders...');
    const result = await makeAuthenticatedRequest('/Orders');
    console.log('Orders response:', result);

    // Handle different response structures
    if (Array.isArray(result)) {
      return result;
    } else if (result.$values && Array.isArray(result.$values)) {
      return result.$values;
    } else if (result.data && Array.isArray(result.data)) {
      return result.data;
    } else {
      console.warn('Unexpected orders response structure:', result);
      return [];
    }
  },

  getOrderById: async (id: number) => {
    console.log('Fetching order by ID:', id);
    const result = await makeAuthenticatedRequest(`/Orders/${id}`);
    console.log('Order by ID response:', result);
    return result;
  },
};

// Categories API
export const categoriesAPI = {
  getAll: async () => {
    console.log('Fetching categories from:', `${API_BASE_URL}/Category`);

    const response = await fetch(`${API_BASE_URL}/Category`);
    console.log('Categories response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.log('Categories error response:', errorText);
      throw new Error(`Failed to fetch categories: ${response.status}`);
    }

    const result = await response.json();
    console.log('Categories response data:', result);

    // Handle different response structures
    if (Array.isArray(result)) {
      return result;
    } else if (result.$values && Array.isArray(result.$values)) {
      // Handle JSON.NET serialization format
      return result.$values;
    } else if (result.data && Array.isArray(result.data)) {
      return result.data;
    } else {
      console.warn('Unexpected categories response structure:', result);
      return [];
    }
  },
};

// Payments API
export const paymentsAPI = {
  getPayments: async () => {
    return makeAuthenticatedRequest('/Payment');
  },
};


