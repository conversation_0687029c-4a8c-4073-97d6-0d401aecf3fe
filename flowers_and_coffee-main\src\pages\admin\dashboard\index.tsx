import React, { useState } from "react";
import {
  Container,
  Grid,
  Paper,
  Typography,
  Box,
  Card,
  CardContent,
  Button,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  LinearProgress,
  TextField,
  MenuItem,
  Divider,
  Avatar,
  Stack,
  IconButton,
} from "@mui/material";
import {
  Dashboard as DashboardIcon,
  TrendingUp,
  ShoppingCart,
  People,
  AttachMoney,
  CalendarToday,
  Assessment,
  LocalShipping,
  Star,
  ArrowUpward,
  ArrowDownward,
  MoreVert,
  Inventory,
  Analytics,
  Settings,
} from "@mui/icons-material";
import { useAppSelector } from "../../../stores/hooks";
import { useNavigate } from "react-router-dom";



const AdminDashboard = () => {
  const { user, isAuthenticated } = useAppSelector((state) => state.Authentication);
  const navigate = useNavigate();
  const [dateFilter, setDateFilter] = useState("7days");

  // Fixed revenue data for demonstration
  const revenueData = [
    { date: "2024-01-15", revenue: 2500000, orders: 15 },
    { date: "2024-01-16", revenue: 3200000, orders: 22 },
    { date: "2024-01-17", revenue: 1800000, orders: 12 },
    { date: "2024-01-18", revenue: 4100000, orders: 28 },
    { date: "2024-01-19", revenue: 3600000, orders: 25 },
    { date: "2024-01-20", revenue: 2900000, orders: 19 },
    { date: "2024-01-21", revenue: 3800000, orders: 26 },
  ];

  // Top selling products (fixed data)
  const topProducts = [
    { name: "Hoa hồng đỏ", sold: 45, revenue: 4500000, growth: 12 },
    { name: "Cà phê Americano", sold: 38, revenue: 3800000, growth: 8 },
    { name: "Hoa ly trắng", sold: 32, revenue: 3200000, growth: -3 },
    { name: "Trà sữa matcha", sold: 29, revenue: 2900000, growth: 15 },
    { name: "Hoa cúc vàng", sold: 25, revenue: 2500000, growth: 5 },
  ];

  // Recent orders (fixed data)
  const recentOrders = [
    { id: "ORD001", customer: "Nguyễn Văn A", amount: 450000, status: "completed", date: "2024-01-21" },
    { id: "ORD002", customer: "Trần Thị B", amount: 320000, status: "pending", date: "2024-01-21" },
    { id: "ORD003", customer: "Lê Văn C", amount: 680000, status: "shipping", date: "2024-01-20" },
    { id: "ORD004", customer: "Phạm Thị D", amount: 290000, status: "completed", date: "2024-01-20" },
    { id: "ORD005", customer: "Hoàng Văn E", amount: 520000, status: "pending", date: "2024-01-19" },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed": return "success";
      case "pending": return "warning";
      case "shipping": return "info";
      case "cancelled": return "error";
      default: return "default";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "completed": return "Hoàn thành";
      case "pending": return "Chờ xử lý";
      case "shipping": return "Đang giao";
      case "cancelled": return "Đã hủy";
      default: return status;
    }
  };



  if (!isAuthenticated || user?.roleId !== 4) {
    return (
      <Container maxWidth="md" sx={{ mt: 4 }}>
        <Alert severity="error">
          Bạn không có quyền truy cập trang này
        </Alert>
      </Container>
    );
  }

  return (
    <Box>
      {/* Welcome Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" sx={{ fontWeight: 700, color: '#1a1a2e', mb: 1 }}>
          Chào mừng trở lại, {user?.fullName}! 👋
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Đây là tổng quan về hoạt động kinh doanh của bạn hôm nay
        </Typography>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            borderRadius: 3,
            overflow: 'hidden',
            position: 'relative',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              right: 0,
              width: 100,
              height: 100,
              background: 'rgba(255,255,255,0.1)',
              borderRadius: '50%',
              transform: 'translate(30px, -30px)'
            }
          }}>
            <CardContent sx={{ position: 'relative', zIndex: 1 }}>
              <Stack direction="row" alignItems="center" justifyContent="space-between" mb={2}>
                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
                  <ShoppingCart sx={{ fontSize: 28 }} />
                </Avatar>
                <Stack direction="row" alignItems="center" color="rgba(255,255,255,0.8)">
                  <ArrowUpward sx={{ fontSize: 16, mr: 0.5 }} />
                  <Typography variant="caption">+12%</Typography>
                </Stack>
              </Stack>
              <Typography variant="h4" sx={{ fontWeight: 700, mb: 0.5 }}>
                150
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.9 }}>
                Tổng đơn hàng
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{
            background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
            color: 'white',
            borderRadius: 3,
            overflow: 'hidden',
            position: 'relative',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              right: 0,
              width: 100,
              height: 100,
              background: 'rgba(255,255,255,0.1)',
              borderRadius: '50%',
              transform: 'translate(30px, -30px)'
            }
          }}>
            <CardContent sx={{ position: 'relative', zIndex: 1 }}>
              <Stack direction="row" alignItems="center" justifyContent="space-between" mb={2}>
                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
                  <AttachMoney sx={{ fontSize: 28 }} />
                </Avatar>
                <Stack direction="row" alignItems="center" color="rgba(255,255,255,0.8)">
                  <ArrowUpward sx={{ fontSize: 16, mr: 0.5 }} />
                  <Typography variant="caption">+8%</Typography>
                </Stack>
              </Stack>
              <Typography variant="h4" sx={{ fontWeight: 700, mb: 0.5 }}>
                22M
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.9 }}>
                Doanh thu (7 ngày)
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{
            background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
            color: 'white',
            borderRadius: 3,
            overflow: 'hidden',
            position: 'relative',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              right: 0,
              width: 100,
              height: 100,
              background: 'rgba(255,255,255,0.1)',
              borderRadius: '50%',
              transform: 'translate(30px, -30px)'
            }
          }}>
            <CardContent sx={{ position: 'relative', zIndex: 1 }}>
              <Stack direction="row" alignItems="center" justifyContent="space-between" mb={2}>
                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
                  <Inventory sx={{ fontSize: 28 }} />
                </Avatar>
                <Stack direction="row" alignItems="center" color="rgba(255,255,255,0.8)">
                  <ArrowUpward sx={{ fontSize: 16, mr: 0.5 }} />
                  <Typography variant="caption">+5%</Typography>
                </Stack>
              </Stack>
              <Typography variant="h4" sx={{ fontWeight: 700, mb: 0.5 }}>
                45
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.9 }}>
                Sản phẩm
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{
            background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
            color: 'white',
            borderRadius: 3,
            overflow: 'hidden',
            position: 'relative',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              right: 0,
              width: 100,
              height: 100,
              background: 'rgba(255,255,255,0.1)',
              borderRadius: '50%',
              transform: 'translate(30px, -30px)'
            }
          }}>
            <CardContent sx={{ position: 'relative', zIndex: 1 }}>
              <Stack direction="row" alignItems="center" justifyContent="space-between" mb={2}>
                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
                  <People sx={{ fontSize: 28 }} />
                </Avatar>
                <Stack direction="row" alignItems="center" color="rgba(255,255,255,0.8)">
                  <ArrowUpward sx={{ fontSize: 16, mr: 0.5 }} />
                  <Typography variant="caption">+15%</Typography>
                </Stack>
              </Stack>
              <Typography variant="h4" sx={{ fontWeight: 700, mb: 0.5 }}>
                320
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.9 }}>
                Người dùng
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Revenue Analytics */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} md={8}>
          <Card sx={{ borderRadius: 3, overflow: 'hidden' }}>
            <Box sx={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              color: 'white',
              p: 3
            }}>
              <Stack direction="row" justifyContent="space-between" alignItems="center">
                <Box>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                    📊 Doanh thu theo ngày
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.9 }}>
                    Theo dõi hiệu suất kinh doanh hàng ngày
                  </Typography>
                </Box>
                <TextField
                  select
                  size="small"
                  value={dateFilter}
                  onChange={(e) => setDateFilter(e.target.value)}
                  sx={{
                    minWidth: 120,
                    '& .MuiOutlinedInput-root': {
                      bgcolor: 'rgba(255,255,255,0.1)',
                      color: 'white',
                      '& fieldset': { borderColor: 'rgba(255,255,255,0.3)' },
                      '&:hover fieldset': { borderColor: 'rgba(255,255,255,0.5)' },
                      '&.Mui-focused fieldset': { borderColor: 'white' }
                    },
                    '& .MuiSelect-icon': { color: 'white' }
                  }}
                >
                  <MenuItem value="7days">7 ngày</MenuItem>
                  <MenuItem value="30days">30 ngày</MenuItem>
                  <MenuItem value="90days">90 ngày</MenuItem>
                </TextField>
              </Stack>
            </Box>
            <CardContent sx={{ p: 0 }}>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow sx={{ bgcolor: 'rgba(102, 126, 234, 0.05)' }}>
                      <TableCell sx={{ fontWeight: 600, color: '#667eea' }}>Ngày</TableCell>
                      <TableCell align="right" sx={{ fontWeight: 600, color: '#667eea' }}>Doanh thu</TableCell>
                      <TableCell align="right" sx={{ fontWeight: 600, color: '#667eea' }}>Đơn hàng</TableCell>
                      <TableCell align="right" sx={{ fontWeight: 600, color: '#667eea' }}>Trung bình/đơn</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {revenueData.map((row, index) => (
                      <TableRow
                        key={row.date}
                        sx={{
                          '&:hover': { bgcolor: 'rgba(102, 126, 234, 0.02)' },
                          borderLeft: index === 0 ? '4px solid #4caf50' : 'none'
                        }}
                      >
                        <TableCell>
                          <Stack direction="row" alignItems="center" spacing={1}>
                            <CalendarToday sx={{ fontSize: 16, color: 'text.secondary' }} />
                            <Typography variant="body2" fontWeight={500}>
                              {new Date(row.date).toLocaleDateString("vi-VN")}
                            </Typography>
                          </Stack>
                        </TableCell>
                        <TableCell align="right">
                          <Typography variant="body2" sx={{
                            color: '#4caf50',
                            fontWeight: 600,
                            fontSize: '1rem'
                          }}>
                            {row.revenue.toLocaleString("vi-VN")}đ
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Chip
                            label={row.orders}
                            size="small"
                            sx={{
                              bgcolor: 'rgba(102, 126, 234, 0.1)',
                              color: '#667eea',
                              fontWeight: 600
                            }}
                          />
                        </TableCell>
                        <TableCell align="right">
                          <Typography variant="body2" fontWeight={500}>
                            {Math.round(row.revenue / row.orders).toLocaleString("vi-VN")}đ
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card sx={{ borderRadius: 3, overflow: 'hidden', height: 'fit-content' }}>
            <Box sx={{
              background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
              color: 'white',
              p: 3
            }}>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                🔥 Sản phẩm bán chạy
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.9 }}>
                Top 5 sản phẩm có doanh số cao nhất
              </Typography>
            </Box>
            <CardContent sx={{ p: 3 }}>
              {topProducts.map((product, index) => (
                <Box key={index} mb={index < topProducts.length - 1 ? 3 : 0}>
                  <Stack direction="row" alignItems="center" spacing={2} mb={2}>
                    <Avatar sx={{
                      bgcolor: index === 0 ? '#ffd700' : index === 1 ? '#c0c0c0' : index === 2 ? '#cd7f32' : '#e0e0e0',
                      color: index < 3 ? 'white' : 'text.secondary',
                      width: 32,
                      height: 32,
                      fontSize: '0.875rem',
                      fontWeight: 600
                    }}>
                      #{index + 1}
                    </Avatar>
                    <Box flex={1}>
                      <Typography variant="body2" fontWeight={600} sx={{ mb: 0.5 }}>
                        {product.name}
                      </Typography>
                      <Stack direction="row" justifyContent="space-between" alignItems="center">
                        <Typography variant="caption" color="text.secondary">
                          Đã bán: {product.sold}
                        </Typography>
                        <Chip
                          label={`${product.growth > 0 ? '+' : ''}${product.growth}%`}
                          color={product.growth > 0 ? "success" : product.growth < 0 ? "error" : "default"}
                          size="small"
                          sx={{ fontSize: '0.7rem', height: 20 }}
                        />
                      </Stack>
                    </Box>
                  </Stack>
                  <Box mb={2}>
                    <Stack direction="row" justifyContent="space-between" alignItems="center" mb={1}>
                      <Typography variant="body2" color="success.main" fontWeight={600}>
                        {product.revenue.toLocaleString("vi-VN")}đ
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {Math.round((product.sold / 50) * 100)}%
                      </Typography>
                    </Stack>
                    <LinearProgress
                      variant="determinate"
                      value={(product.sold / 50) * 100}
                      sx={{
                        height: 8,
                        borderRadius: 4,
                        bgcolor: 'rgba(0,0,0,0.05)',
                        '& .MuiLinearProgress-bar': {
                          borderRadius: 4,
                          background: index === 0 ? 'linear-gradient(90deg, #ffd700, #ffed4e)' :
                                     index === 1 ? 'linear-gradient(90deg, #c0c0c0, #e8e8e8)' :
                                     index === 2 ? 'linear-gradient(90deg, #cd7f32, #daa520)' :
                                     'linear-gradient(90deg, #667eea, #764ba2)'
                        }
                      }}
                    />
                  </Box>
                  {index < topProducts.length - 1 && <Divider />}
                </Box>
              ))}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Recent Orders */}
      <Card sx={{ borderRadius: 3, overflow: 'hidden', mb: 4 }}>
        <Box sx={{
          background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
          color: 'white',
          p: 3
        }}>
          <Stack direction="row" justifyContent="space-between" alignItems="center">
            <Box>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                📋 Đơn hàng gần đây
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.9 }}>
                5 đơn hàng mới nhất cần xử lý
              </Typography>
            </Box>
            <IconButton sx={{ color: 'white' }}>
              <MoreVert />
            </IconButton>
          </Stack>
        </Box>
        <CardContent sx={{ p: 0 }}>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow sx={{ bgcolor: 'rgba(79, 172, 254, 0.05)' }}>
                  <TableCell sx={{ fontWeight: 600, color: '#4facfe' }}>Mã đơn</TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#4facfe' }}>Khách hàng</TableCell>
                  <TableCell align="right" sx={{ fontWeight: 600, color: '#4facfe' }}>Số tiền</TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#4facfe' }}>Trạng thái</TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#4facfe' }}>Ngày</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {recentOrders.map((order, index) => (
                  <TableRow
                    key={order.id}
                    sx={{
                      '&:hover': { bgcolor: 'rgba(79, 172, 254, 0.02)' },
                      borderLeft: order.status === 'pending' ? '4px solid #ff9800' : 'none'
                    }}
                  >
                    <TableCell>
                      <Typography variant="body2" fontWeight={600} color="primary">
                        {order.id}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Stack direction="row" alignItems="center" spacing={2}>
                        <Avatar sx={{ width: 32, height: 32, bgcolor: '#4facfe', fontSize: '0.875rem' }}>
                          {order.customer.charAt(0)}
                        </Avatar>
                        <Typography variant="body2" fontWeight={500}>
                          {order.customer}
                        </Typography>
                      </Stack>
                    </TableCell>
                    <TableCell align="right">
                      <Typography variant="body2" sx={{
                        fontWeight: 600,
                        color: '#4caf50',
                        fontSize: '1rem'
                      }}>
                        {order.amount.toLocaleString("vi-VN")}đ
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={getStatusText(order.status)}
                        color={getStatusColor(order.status) as any}
                        size="small"
                        sx={{ fontWeight: 500 }}
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color="text.secondary">
                        {new Date(order.date).toLocaleDateString("vi-VN")}
                      </Typography>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card sx={{ borderRadius: 3, overflow: 'hidden' }}>
        <Box sx={{
          background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
          color: 'white',
          p: 3
        }}>
          <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
            ⚡ Thao tác nhanh
          </Typography>
          <Typography variant="body2" sx={{ opacity: 0.9 }}>
            Truy cập nhanh các chức năng quản lý chính
          </Typography>
        </Box>
        <CardContent sx={{ p: 4 }}>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6} md={4}>
              <Button
                variant="contained"
                fullWidth
                size="large"
                onClick={() => navigate("/admin/products")}
                startIcon={<Inventory />}
                sx={{
                  py: 2,
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  borderRadius: 3,
                  boxShadow: '0 4px 16px rgba(102, 126, 234, 0.3)',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
                    boxShadow: '0 6px 20px rgba(102, 126, 234, 0.4)',
                    transform: 'translateY(-2px)'
                  },
                  transition: 'all 0.3s ease'
                }}
              >
                Quản lý sản phẩm
              </Button>
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <Button
                variant="contained"
                fullWidth
                size="large"
                onClick={() => navigate("/admin/orders")}
                startIcon={<LocalShipping />}
                sx={{
                  py: 2,
                  background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                  borderRadius: 3,
                  boxShadow: '0 4px 16px rgba(240, 147, 251, 0.3)',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #e081e9 0%, #e3455a 100%)',
                    boxShadow: '0 6px 20px rgba(240, 147, 251, 0.4)',
                    transform: 'translateY(-2px)'
                  },
                  transition: 'all 0.3s ease'
                }}
              >
                Quản lý đơn hàng
              </Button>
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <Button
                variant="contained"
                fullWidth
                size="large"
                onClick={() => navigate("/admin/users")}
                startIcon={<People />}
                sx={{
                  py: 2,
                  background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                  borderRadius: 3,
                  boxShadow: '0 4px 16px rgba(79, 172, 254, 0.3)',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #3d9aec 0%, #00d9ec 100%)',
                    boxShadow: '0 6px 20px rgba(79, 172, 254, 0.4)',
                    transform: 'translateY(-2px)'
                  },
                  transition: 'all 0.3s ease'
                }}
              >
                Quản lý người dùng
              </Button>
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <Button
                variant="outlined"
                fullWidth
                size="large"
                onClick={() => navigate("/admin/analytics")}
                startIcon={<Analytics />}
                sx={{
                  py: 2,
                  borderColor: '#667eea',
                  color: '#667eea',
                  borderRadius: 3,
                  borderWidth: 2,
                  '&:hover': {
                    borderColor: '#5a6fd8',
                    bgcolor: 'rgba(102, 126, 234, 0.05)',
                    borderWidth: 2,
                    transform: 'translateY(-2px)'
                  },
                  transition: 'all 0.3s ease'
                }}
              >
                Thống kê chi tiết
              </Button>
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <Button
                variant="outlined"
                fullWidth
                size="large"
                onClick={() => navigate("/admin/settings")}
                startIcon={<Settings />}
                sx={{
                  py: 2,
                  borderColor: '#f093fb',
                  color: '#f093fb',
                  borderRadius: 3,
                  borderWidth: 2,
                  '&:hover': {
                    borderColor: '#e081e9',
                    bgcolor: 'rgba(240, 147, 251, 0.05)',
                    borderWidth: 2,
                    transform: 'translateY(-2px)'
                  },
                  transition: 'all 0.3s ease'
                }}
              >
                Cài đặt hệ thống
              </Button>
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <Button
                variant="outlined"
                fullWidth
                size="large"
                onClick={() => navigate("/")}
                startIcon={<Star />}
                sx={{
                  py: 2,
                  borderColor: '#4facfe',
                  color: '#4facfe',
                  borderRadius: 3,
                  borderWidth: 2,
                  '&:hover': {
                    borderColor: '#3d9aec',
                    bgcolor: 'rgba(79, 172, 254, 0.05)',
                    borderWidth: 2,
                    transform: 'translateY(-2px)'
                  },
                  transition: 'all 0.3s ease'
                }}
              >
                Xem website
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Box>
  );
};

export default AdminDashboard;
