<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Register API</title>
</head>
<body>
    <h1>Test Register API</h1>
    <form id="registerForm">
        <div>
            <label>Full Name:</label>
            <input type="text" id="fullName" value="Test User" required>
        </div>
        <div>
            <label>Email:</label>
            <input type="email" id="email" value="<EMAIL>" required>
        </div>
        <div>
            <label>Password:</label>
            <input type="password" id="password" value="123456" required>
        </div>
        <div>
            <label>Phone:</label>
            <input type="text" id="phone" value="0123456789" required>
        </div>
        <div>
            <label>Address:</label>
            <input type="text" id="address" value="123 Test Street, Test City" required>
        </div>
        <div>
            <label>Gender:</label>
            <select id="gender" required>
                <option value="">Select Gender</option>
                <option value="Nam" selected>Nam</option>
                <option value="Nữ">Nữ</option>
                <option value="Khác">Khác</option>
            </select>
        </div>
        <div>
            <label>Birth Date:</label>
            <input type="date" id="birthDate" value="1990-01-01" required>
        </div>
        <button type="submit">Register</button>
    </form>

    <div id="result"></div>

    <script>
        document.getElementById('registerForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const formData = {
                fullName: document.getElementById('fullName').value,
                email: document.getElementById('email').value,
                password: document.getElementById('password').value,
                phone: document.getElementById('phone').value,
                address: document.getElementById('address').value,
                gender: document.getElementById('gender').value,
                birthDate: document.getElementById('birthDate').value
            };

            try {
                const response = await fetch('https://jucieandflower20250517151939.azurewebsites.net/api/Auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();

                if (response.ok) {
                    document.getElementById('result').innerHTML = `
                        <h3 style="color: green;">Success!</h3>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    `;
                } else {
                    document.getElementById('result').innerHTML = `
                        <h3 style="color: red;">Error!</h3>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                document.getElementById('result').innerHTML = `
                    <h3 style="color: red;">Network Error!</h3>
                    <pre>${error.message}</pre>
                `;
            }
        });
    </script>
</body>
</html>
