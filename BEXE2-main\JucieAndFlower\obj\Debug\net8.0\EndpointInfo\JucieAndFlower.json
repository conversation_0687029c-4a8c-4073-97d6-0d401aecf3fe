{"openapi": "3.0.1", "info": {"title": "JucieAnd<PERSON>lower", "version": "v1"}, "paths": {"/api/Auth/register": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserRegisterDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserRegisterDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserRegisterDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Auth/logout": {"post": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}}, "/api/Auth/refresh-token": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TokenModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TokenModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Auth/update-profile": {"put": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpdateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserUpdateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserUpdateDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Auth/profile": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}}, "/api/Cart": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CartItemCreateDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CartItemCreateDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CartItemCreateDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Cart/{productId}": {"delete": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "productId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Category": {"get": {"tags": ["Category"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Category"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryNoID"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CategoryNoID"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CategoryNoID"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Category/{id}": {"get": {"tags": ["Category"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Category"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryNoID"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CategoryNoID"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CategoryNoID"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Category"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Orders": {"get": {"tags": ["Orders"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Orders/payment-return": {"get": {"tags": ["Orders"], "responses": {"200": {"description": "OK"}}}}, "/api/Orders/from-cart": {"post": {"tags": ["Orders"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderFromCartDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrderFromCartDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OrderFromCartDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Payment": {"get": {"tags": ["Payment"], "responses": {"200": {"description": "OK"}}}}, "/api/ProductDetail": {"get": {"tags": ["ProductDetail"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["ProductDetail"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductDetailCreateDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDetailCreateDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductDetailCreateDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/ProductDetail/{id}": {"get": {"tags": ["ProductDetail"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["ProductDetail"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductDetailCreateDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDetailCreateDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductDetailCreateDTO"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["ProductDetail"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Products": {"get": {"tags": ["Products"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Products"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductCreateUpdateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductCreateUpdateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductCreateUpdateDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Products/{id}": {"get": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductCreateUpdateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductCreateUpdateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductCreateUpdateDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Promotion": {"get": {"tags": ["Promotion"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Promotion"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PromotionCreateUpdateDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PromotionCreateUpdateDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PromotionCreateUpdateDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Promotion/{id}": {"get": {"tags": ["Promotion"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Promotion"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PromotionCreateUpdateDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PromotionCreateUpdateDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PromotionCreateUpdateDTO"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Promotion"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Workshop": {"get": {"tags": ["Workshop"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Workshop"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkshopDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkshopDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WorkshopDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Workshop/{id}": {"get": {"tags": ["Workshop"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Workshop"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkshopDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkshopDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WorkshopDTO"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Workshop"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/WorkshopTicket": {"get": {"tags": ["WorkshopTicket"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["WorkshopTicket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkshopTicketDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkshopTicketDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WorkshopTicketDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/WorkshopTicket/{id}": {"get": {"tags": ["WorkshopTicket"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["WorkshopTicket"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkshopTicketDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkshopTicketDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WorkshopTicketDTO"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["WorkshopTicket"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"CartItemCreateDTO": {"type": "object", "properties": {"productId": {"type": "integer", "format": "int32"}, "quantity": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "CategoryNoID": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "LoginRequest": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}}, "additionalProperties": false}, "OrderFromCartDTO": {"type": "object", "properties": {"deliveryAddress": {"type": "string", "nullable": true}, "promotionCode": {"type": "string", "nullable": true}, "note": {"type": "string", "nullable": true}, "selectedCartItemIds": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "ProductCreateUpdateDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "price": {"type": "number", "format": "double"}, "imageUrl": {"type": "string", "nullable": true}, "categoryId": {"type": "integer", "format": "int32"}, "isAvailable": {"type": "boolean"}}, "additionalProperties": false}, "ProductDetailCreateDTO": {"type": "object", "properties": {"productId": {"type": "integer", "format": "int32", "nullable": true}, "size": {"type": "string", "nullable": true}, "color": {"type": "string", "nullable": true}, "flowerType": {"type": "string", "nullable": true}, "extraPrice": {"type": "number", "format": "double", "nullable": true}}, "additionalProperties": false}, "PromotionCreateUpdateDTO": {"type": "object", "properties": {"code": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "discountPercent": {"type": "integer", "format": "int32", "nullable": true}, "maxDiscount": {"type": "number", "format": "double", "nullable": true}, "startDate": {"type": "string", "format": "date", "nullable": true}, "endDate": {"type": "string", "format": "date", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "TokenModel": {"type": "object", "properties": {"accessToken": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserRegisterDto": {"type": "object", "properties": {"fullName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "gender": {"type": "string", "nullable": true}, "birthDate": {"type": "string", "format": "date", "nullable": true}}, "additionalProperties": false}, "UserUpdateDto": {"type": "object", "properties": {"fullName": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "gender": {"type": "string", "nullable": true}, "birthDate": {"type": "string", "format": "date", "nullable": true}, "oldPassword": {"type": "string", "nullable": true}, "newPassword": {"type": "string", "nullable": true}, "confirmPassword": {"type": "string", "nullable": true}}, "additionalProperties": false}, "WorkshopDTO": {"type": "object", "properties": {"title": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "location": {"type": "string", "nullable": true}, "eventDate": {"type": "string", "format": "date-time", "nullable": true}, "maxAttendees": {"type": "integer", "format": "int32", "nullable": true}, "price": {"type": "number", "format": "double", "nullable": true}, "imageUrl": {"type": "string", "nullable": true}}, "additionalProperties": false}, "WorkshopTicketDTO": {"type": "object", "properties": {"workshopId": {"type": "integer", "format": "int32", "nullable": true}, "ticketType": {"type": "string", "nullable": true}, "price": {"type": "number", "format": "double", "nullable": true}, "quantity": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT Authorization header using the Bear<PERSON> scheme (Example: 'Bearer <token>')", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}