import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Alert,
  Card,
  CardContent,
  CircularProgress,
} from "@mui/material";
import { Add } from "@mui/icons-material";
import { useAppSelector } from "../../../stores/hooks";
import { productsAPI, categoriesAPI } from "../../../services/api";
import { Product, Category } from "../../../types";
import { toast } from "react-toastify";
import { useNavigate } from "react-router-dom";

const SimpleAdminProducts = () => {
  const { user, isAuthenticated } = useAppSelector((state) => state.Authentication);
  const navigate = useNavigate();

  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    console.log('SimpleAdminProducts - Auth state:', {
      isAuthenticated,
      user,
      userRoleId: user?.roleId,
      hasCorrectRole: user?.roleId === 4
    });

    if (!isAuthenticated || user?.roleId !== 4) {
      console.log('SimpleAdminProducts - Access denied, redirecting to home');
      navigate("/");
      return;
    }
    
    console.log('SimpleAdminProducts - Access granted, loading data');
    loadProducts();
    loadCategories();
  }, [isAuthenticated, user, navigate]);

  const loadProducts = async () => {
    try {
      setLoading(true);
      console.log('Loading products for admin...');
      const data = await productsAPI.getAll();
      console.log('Admin products loaded:', data);

      if (Array.isArray(data)) {
        setProducts(data);
      } else {
        console.error('Products data is not an array:', data);
        setProducts([]);
        toast.error("Dữ liệu sản phẩm không hợp lệ");
      }
    } catch (error: any) {
      console.error('Error loading products for admin:', error);
      toast.error(`Không thể tải danh sách sản phẩm: ${error.message}`);
      setProducts([]);
    } finally {
      setLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      console.log('Loading categories for admin...');
      const data = await categoriesAPI.getAll();
      console.log('Admin categories loaded:', data);

      if (Array.isArray(data)) {
        setCategories(data);
      } else {
        console.error('Categories data is not an array:', data);
        setCategories([]);
        toast.error("Dữ liệu danh mục không hợp lệ");
      }
    } catch (error: any) {
      console.error('Error loading categories for admin:', error);
      toast.error(`Không thể tải danh sách danh mục: ${error.message}`);
      setCategories([]);
    }
  };

  console.log('SimpleAdminProducts render - Auth state:', {
    isAuthenticated,
    user,
    userRoleId: user?.roleId,
    hasCorrectRole: user?.roleId === 4
  });

  if (!isAuthenticated || user?.roleId !== 4) {
    console.log('SimpleAdminProducts render - Showing access denied message');
    return (
      <Container maxWidth="md" sx={{ mt: 4 }}>
        <Alert severity="error">
          Bạn không có quyền truy cập trang này
          <br />
          Debug: isAuthenticated={String(isAuthenticated)}, userRoleId={user?.roleId}
        </Alert>
      </Container>
    );
  }

  console.log('SimpleAdminProducts render - Showing admin interface');

  return (
    <Container maxWidth="lg" sx={{ mt: 4 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" sx={{ fontWeight: 700, color: '#1a1a2e', mb: 1 }}>
          📦 Quản lý sản phẩm (Simple)
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Quản lý toàn bộ sản phẩm trong hệ thống
        </Typography>
      </Box>

      {/* Debug Info */}
      <Card sx={{ mb: 3, border: '2px solid #4caf50' }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2, color: '#4caf50' }}>
            ✅ Trang đã load thành công!
          </Typography>
          <Typography variant="body2" sx={{ mb: 1 }}>
            User: {user?.fullName} (ID: {user?.userId})
          </Typography>
          <Typography variant="body2" sx={{ mb: 1 }}>
            Role ID: {user?.roleId}
          </Typography>
          <Typography variant="body2" sx={{ mb: 1 }}>
            Is Admin: {user?.roleId === 4 ? 'Yes' : 'No'}
          </Typography>
          <Typography variant="body2" sx={{ mb: 1 }}>
            Products loaded: {products.length}
          </Typography>
          <Typography variant="body2">
            Categories loaded: {categories.length}
          </Typography>
        </CardContent>
      </Card>

      {/* Loading */}
      {loading && (
        <Box display="flex" justifyContent="center" my={4}>
          <CircularProgress />
        </Box>
      )}

      {/* Simple Controls */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h6">
              Sản phẩm ({products.length})
            </Typography>
            <Button
              variant="contained"
              startIcon={<Add />}
              sx={{ bgcolor: '#4caf50' }}
            >
              Thêm sản phẩm
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Simple Product List */}
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Danh sách sản phẩm
          </Typography>
          {products.length === 0 ? (
            <Typography variant="body1" color="text.secondary">
              Chưa có sản phẩm nào
            </Typography>
          ) : (
            <Box>
              {products.map((product) => (
                <Box
                  key={product.productId}
                  sx={{
                    p: 2,
                    border: '1px solid #e0e0e0',
                    borderRadius: 1,
                    mb: 1
                  }}
                >
                  <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                    {product.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {product.description}
                  </Typography>
                  <Typography variant="body2">
                    Giá: {product.price.toLocaleString()} VND
                  </Typography>
                </Box>
              ))}
            </Box>
          )}
        </CardContent>
      </Card>
    </Container>
  );
};

export default SimpleAdminProducts;
