import React, { useState, useEffect } from "react";
import {
  Con<PERSON>er,
  Paper,
  Typography,
  TextField,
  Button,
  Grid,
  Box,
  Avatar,
  Divider,
  Alert,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Card,
  CardContent,
  CardHeader,
  Chip,
  Stack,
  IconButton,
  Tooltip,
} from "@mui/material";
import {
  Person,
  Edit,
  Save,
  Cancel,
  Email,
  Phone,
  LocationOn,
  Cake,
  Wc,
  Lock,
  Visibility,
  VisibilityOff,
  AccountCircle,
  Security
} from "@mui/icons-material";
import { useAppSelector, useAppDispatch } from "../../stores/hooks";
import { updateProfile } from "../../stores/reducers/Authentication";
import { authAPI } from "../../services/api";
import { toast } from "react-toastify";

const ProfilePage = () => {
  const { user } = useAppSelector((state) => state.Authentication);
  const dispatch = useAppDispatch();

  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const [formData, setFormData] = useState({
    fullName: user?.fullName || "",
    email: user?.email || "",
    phone: user?.phone || "",
    address: user?.address || "",
    gender: user?.gender || "",
    birthDate: user?.birthDate ? user.birthDate.split('T')[0] : "",
  });

  const [passwordData, setPasswordData] = useState({
    oldPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  const [showPasswordForm, setShowPasswordForm] = useState(false);
  const [showPasswords, setShowPasswords] = useState({
    old: false,
    new: false,
    confirm: false,
  });

  useEffect(() => {
    if (user) {
      setFormData({
        fullName: user.fullName || "",
        email: user.email || "",
        phone: user.phone || "",
        address: user.address || "",
        gender: user.gender || "",
        birthDate: user.birthDate ? user.birthDate.split('T')[0] : "",
      });
    }
  }, [user]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePasswordChange = (field: string, value: string) => {
    setPasswordData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = async () => {
    setError("");
    setLoading(true);

    try {
      // Validate password if changing
      if (showPasswordForm) {
        if (!passwordData.oldPassword || !passwordData.newPassword || !passwordData.confirmPassword) {
          setError("Vui lòng điền đầy đủ thông tin mật khẩu");
          setLoading(false);
          return;
        }

        if (passwordData.newPassword !== passwordData.confirmPassword) {
          setError("Mật khẩu mới và xác nhận mật khẩu không khớp");
          setLoading(false);
          return;
        }

        if (passwordData.newPassword.length < 6) {
          setError("Mật khẩu mới phải có ít nhất 6 ký tự");
          setLoading(false);
          return;
        }
      }

      const updateData = {
        fullName: formData.fullName,
        phone: formData.phone,
        address: formData.address,
        gender: formData.gender,
        birthDate: formData.birthDate,
        ...(showPasswordForm && passwordData.newPassword ? {
          oldPassword: passwordData.oldPassword,
          newPassword: passwordData.newPassword,
          confirmPassword: passwordData.confirmPassword
        } : {})
      };

      // Call update API (returns success message)
      await authAPI.updateProfile(updateData);

      // Get fresh user data after update
      const updatedUser = await authAPI.getProfile();
      dispatch(updateProfile(updatedUser));

      setIsEditing(false);
      setShowPasswordForm(false);
      setPasswordData({ oldPassword: "", newPassword: "", confirmPassword: "" });
      toast.success("Cập nhật thông tin thành công!");
    } catch (error: any) {
      const errorMessage = error.message || "Có lỗi xảy ra khi cập nhật thông tin";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    setShowPasswordForm(false);
    setError("");
    setPasswordData({ oldPassword: "", newPassword: "", confirmPassword: "" });
    setShowPasswords({ old: false, new: false, confirm: false });

    if (user) {
      setFormData({
        fullName: user.fullName || "",
        email: user.email || "",
        phone: user.phone || "",
        address: user.address || "",
        gender: user.gender || "",
        birthDate: user.birthDate ? user.birthDate.split('T')[0] : "",
      });
    }
  };

  const togglePasswordVisibility = (field: 'old' | 'new' | 'confirm') => {
    setShowPasswords(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  const getRoleDisplayName = (roleId: number) => {
    switch (roleId) {
      case 1: return "Khách hàng";
      case 2: return "Nhân viên";
      case 4: return "Quản trị viên";
      default: return "Khách";
    }
  };

  const getRoleColor = (roleId: number) => {
    switch (roleId) {
      case 1: return "primary";
      case 2: return "secondary";
      case 4: return "error";
      default: return "default";
    }
  };

  if (!user) {
    return (
      <Container maxWidth="md" sx={{ mt: 4 }}>
        <Alert severity="warning">
          Vui lòng đăng nhập để xem thông tin cá nhân
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Header Section */}
      <Card elevation={3} sx={{ mb: 3, background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}>
        <CardContent sx={{ p: 4 }}>
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <Box display="flex" alignItems="center">
              <Avatar
                sx={{
                  width: 100,
                  height: 100,
                  mr: 3,
                  bgcolor: "rgba(255,255,255,0.2)",
                  border: "3px solid rgba(255,255,255,0.3)"
                }}
              >
                <AccountCircle sx={{ fontSize: 60, color: "white" }} />
              </Avatar>
              <Box>
                <Typography variant="h4" sx={{ color: "white", fontWeight: 600, mb: 1 }}>
                  {user?.fullName || "Người dùng"}
                </Typography>
                <Stack direction="row" spacing={2} alignItems="center">
                  <Chip
                    label={getRoleDisplayName(user?.roleId || 1)}
                    color={getRoleColor(user?.roleId || 1) as any}
                    size="small"
                    sx={{ fontWeight: 500 }}
                  />
                  <Typography variant="body2" sx={{ color: "rgba(255,255,255,0.8)" }}>
                    <Email sx={{ fontSize: 16, mr: 0.5, verticalAlign: "middle" }} />
                    {user?.email}
                  </Typography>
                </Stack>
              </Box>
            </Box>
            {!isEditing && (
              <Button
                variant="contained"
                startIcon={<Edit />}
                onClick={() => setIsEditing(true)}
                sx={{
                  bgcolor: "rgba(255,255,255,0.2)",
                  color: "white",
                  '&:hover': { bgcolor: "rgba(255,255,255,0.3)" }
                }}
              >
                Chỉnh sửa
              </Button>
            )}
          </Box>
        </CardContent>
      </Card>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Personal Information Card */}
        <Grid item xs={12} md={8}>
          <Card elevation={2}>
            <CardHeader
              title="Thông tin cá nhân"
              avatar={<Person color="primary" />}
              sx={{ bgcolor: "grey.50" }}
            />
            <CardContent sx={{ p: 3 }}>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Họ và tên"
                    value={formData.fullName}
                    onChange={(e) => handleInputChange("fullName", e.target.value)}
                    disabled={!isEditing}
                    variant={isEditing ? "outlined" : "filled"}
                    InputProps={{
                      startAdornment: <Person sx={{ mr: 1, color: "action.active" }} />
                    }}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Email"
                    value={formData.email}
                    disabled={true}
                    variant="filled"
                    helperText="Email không thể thay đổi"
                    InputProps={{
                      startAdornment: <Email sx={{ mr: 1, color: "action.active" }} />
                    }}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Số điện thoại"
                    value={formData.phone}
                    onChange={(e) => handleInputChange("phone", e.target.value)}
                    disabled={!isEditing}
                    variant={isEditing ? "outlined" : "filled"}
                    InputProps={{
                      startAdornment: <Phone sx={{ mr: 1, color: "action.active" }} />
                    }}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth disabled={!isEditing}>
                    <InputLabel>Giới tính</InputLabel>
                    <Select
                      value={formData.gender}
                      label="Giới tính"
                      onChange={(e) => handleInputChange("gender", e.target.value)}
                      variant={isEditing ? "outlined" : "filled"}
                      startAdornment={<Wc sx={{ mr: 1, color: "action.active" }} />}
                    >
                      <MenuItem value="Male">Nam</MenuItem>
                      <MenuItem value="Female">Nữ</MenuItem>
                      <MenuItem value="Other">Khác</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Ngày sinh"
                    type="date"
                    value={formData.birthDate}
                    onChange={(e) => handleInputChange("birthDate", e.target.value)}
                    disabled={!isEditing}
                    variant={isEditing ? "outlined" : "filled"}
                    InputLabelProps={{ shrink: true }}
                    InputProps={{
                      startAdornment: <Cake sx={{ mr: 1, color: "action.active" }} />
                    }}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Địa chỉ"
                    value={formData.address}
                    onChange={(e) => handleInputChange("address", e.target.value)}
                    disabled={!isEditing}
                    variant={isEditing ? "outlined" : "filled"}
                    multiline
                    rows={2}
                    InputProps={{
                      startAdornment: <LocationOn sx={{ mr: 1, color: "action.active", alignSelf: "flex-start", mt: 1 }} />
                    }}
                  />
                </Grid>
              </Grid>

              {isEditing && (
                <Box mt={3} display="flex" justifyContent="flex-end" gap={2}>
                  <Button
                    variant="outlined"
                    color="secondary"
                    startIcon={<Cancel />}
                    onClick={handleCancel}
                    disabled={loading}
                  >
                    Hủy
                  </Button>
                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<Save />}
                    onClick={handleSave}
                    disabled={loading}
                  >
                    {loading ? <CircularProgress size={24} /> : "Lưu thay đổi"}
                  </Button>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Security Settings Card */}
        <Grid item xs={12} md={4}>
          <Card elevation={2}>
            <CardHeader
              title="Bảo mật"
              avatar={<Security color="primary" />}
              sx={{ bgcolor: "grey.50" }}
            />
            <CardContent sx={{ p: 3 }}>
              {!showPasswordForm ? (
                <Box textAlign="center">
                  <Lock sx={{ fontSize: 48, color: "grey.400", mb: 2 }} />
                  <Typography variant="body2" color="text.secondary" mb={3}>
                    Thay đổi mật khẩu để bảo vệ tài khoản của bạn
                  </Typography>
                  <Button
                    variant="outlined"
                    startIcon={<Lock />}
                    onClick={() => setShowPasswordForm(true)}
                    fullWidth
                  >
                    Đổi mật khẩu
                  </Button>
                </Box>
              ) : (
                <Box>
                  <TextField
                    fullWidth
                    label="Mật khẩu cũ"
                    type={showPasswords.old ? "text" : "password"}
                    value={passwordData.oldPassword}
                    onChange={(e) => handlePasswordChange("oldPassword", e.target.value)}
                    margin="normal"
                    size="small"
                    InputProps={{
                      startAdornment: <Lock sx={{ mr: 1, color: "action.active" }} />,
                      endAdornment: (
                        <IconButton
                          onClick={() => togglePasswordVisibility('old')}
                          edge="end"
                          size="small"
                        >
                          {showPasswords.old ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      )
                    }}
                  />
                  <TextField
                    fullWidth
                    label="Mật khẩu mới"
                    type={showPasswords.new ? "text" : "password"}
                    value={passwordData.newPassword}
                    onChange={(e) => handlePasswordChange("newPassword", e.target.value)}
                    margin="normal"
                    size="small"
                    InputProps={{
                      startAdornment: <Lock sx={{ mr: 1, color: "action.active" }} />,
                      endAdornment: (
                        <IconButton
                          onClick={() => togglePasswordVisibility('new')}
                          edge="end"
                          size="small"
                        >
                          {showPasswords.new ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      )
                    }}
                  />
                  <TextField
                    fullWidth
                    label="Xác nhận mật khẩu"
                    type={showPasswords.confirm ? "text" : "password"}
                    value={passwordData.confirmPassword}
                    onChange={(e) => handlePasswordChange("confirmPassword", e.target.value)}
                    margin="normal"
                    size="small"
                    InputProps={{
                      startAdornment: <Lock sx={{ mr: 1, color: "action.active" }} />,
                      endAdornment: (
                        <IconButton
                          onClick={() => togglePasswordVisibility('confirm')}
                          edge="end"
                          size="small"
                        >
                          {showPasswords.confirm ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      )
                    }}
                  />
                  <Box mt={3} display="flex" gap={1}>
                    <Button
                      variant="outlined"
                      color="secondary"
                      onClick={() => {
                        setShowPasswordForm(false);
                        setPasswordData({ oldPassword: "", newPassword: "", confirmPassword: "" });
                        setShowPasswords({ old: false, new: false, confirm: false });
                      }}
                      disabled={loading}
                      size="small"
                      fullWidth
                    >
                      Hủy
                    </Button>
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={handleSave}
                      disabled={loading || !passwordData.oldPassword || !passwordData.newPassword || !passwordData.confirmPassword}
                      size="small"
                      fullWidth
                    >
                      {loading ? <CircularProgress size={20} /> : "Lưu"}
                    </Button>
                  </Box>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
};

export default ProfilePage;
