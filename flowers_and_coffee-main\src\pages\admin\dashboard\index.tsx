import React, { useState } from "react";
import {
  Container,
  Grid,
  Paper,
  Typography,
  Box,
  Card,
  CardContent,
  Button,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  LinearProgress,
  TextField,
  MenuItem,
  Divider,
  Avatar,
  Stack,
  IconButton,
} from "@mui/material";
import {
  Dashboard as DashboardIcon,
  TrendingUp,
  ShoppingCart,
  People,
  AttachMoney,
  CalendarToday,
  Assessment,
  LocalShipping,
  Star,
  ArrowUpward,
  ArrowDownward,
  MoreVert,
  Inventory,
  Analytics,
  Settings,
} from "@mui/icons-material";
import { useAppSelector } from "../../../stores/hooks";
import { useNavigate } from "react-router-dom";



const AdminDashboard = () => {
  const { user, isAuthenticated } = useAppSelector((state) => state.Authentication);
  const navigate = useNavigate();
  const [dateFilter, setDateFilter] = useState("7days");

  // Fixed revenue data for demonstration
  const revenueData = [
    { date: "2024-01-15", revenue: 2500000, orders: 15 },
    { date: "2024-01-16", revenue: 3200000, orders: 22 },
    { date: "2024-01-17", revenue: 1800000, orders: 12 },
    { date: "2024-01-18", revenue: 4100000, orders: 28 },
    { date: "2024-01-19", revenue: 3600000, orders: 25 },
    { date: "2024-01-20", revenue: 2900000, orders: 19 },
    { date: "2024-01-21", revenue: 3800000, orders: 26 },
  ];

  // Top selling products (fixed data)
  const topProducts = [
    { name: "Hoa hồng đỏ", sold: 45, revenue: 4500000, growth: 12 },
    { name: "Cà phê Americano", sold: 38, revenue: 3800000, growth: 8 },
    { name: "Hoa ly trắng", sold: 32, revenue: 3200000, growth: -3 },
    { name: "Trà sữa matcha", sold: 29, revenue: 2900000, growth: 15 },
    { name: "Hoa cúc vàng", sold: 25, revenue: 2500000, growth: 5 },
  ];

  // Recent orders (fixed data)
  const recentOrders = [
    { id: "ORD001", customer: "Nguyễn Văn A", amount: 450000, status: "completed", date: "2024-01-21" },
    { id: "ORD002", customer: "Trần Thị B", amount: 320000, status: "pending", date: "2024-01-21" },
    { id: "ORD003", customer: "Lê Văn C", amount: 680000, status: "shipping", date: "2024-01-20" },
    { id: "ORD004", customer: "Phạm Thị D", amount: 290000, status: "completed", date: "2024-01-20" },
    { id: "ORD005", customer: "Hoàng Văn E", amount: 520000, status: "pending", date: "2024-01-19" },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed": return "success";
      case "pending": return "warning";
      case "shipping": return "info";
      case "cancelled": return "error";
      default: return "default";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "completed": return "Hoàn thành";
      case "pending": return "Chờ xử lý";
      case "shipping": return "Đang giao";
      case "cancelled": return "Đã hủy";
      default: return status;
    }
  };



  if (!isAuthenticated || user?.roleId !== 4) {
    return (
      <Container maxWidth="md" sx={{ mt: 4 }}>
        <Alert severity="error">
          Bạn không có quyền truy cập trang này
        </Alert>
      </Container>
    );
  }

  return (
    <Box>
      {/* Simple Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" sx={{ fontWeight: 600, color: '#333', mb: 1 }}>
          Dashboard
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Tổng quan hệ thống
        </Typography>
      </Box>

      {/* Simple Stats Cards */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ p: 3, textAlign: 'center', boxShadow: 1 }}>
            <ShoppingCart sx={{ fontSize: 40, color: '#1976d2', mb: 2 }} />
            <Typography variant="h4" sx={{ fontWeight: 600, mb: 1 }}>
              150
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Tổng đơn hàng
            </Typography>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ p: 3, textAlign: 'center', boxShadow: 1 }}>
            <AttachMoney sx={{ fontSize: 40, color: '#4caf50', mb: 2 }} />
            <Typography variant="h4" sx={{ fontWeight: 600, mb: 1 }}>
              22M
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Doanh thu (7 ngày)
            </Typography>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ p: 3, textAlign: 'center', boxShadow: 1 }}>
            <Inventory sx={{ fontSize: 40, color: '#ff9800', mb: 2 }} />
            <Typography variant="h4" sx={{ fontWeight: 600, mb: 1 }}>
              45
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Sản phẩm
            </Typography>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ p: 3, textAlign: 'center', boxShadow: 1 }}>
            <People sx={{ fontSize: 40, color: '#9c27b0', mb: 2 }} />
            <Typography variant="h4" sx={{ fontWeight: 600, mb: 1 }}>
              320
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Người dùng
            </Typography>
          </Card>
        </Grid>
      </Grid>

      {/* Simple Tables */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} md={8}>
          <Card sx={{ p: 3 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                Doanh thu theo ngày
              </Typography>
              <TextField
                select
                size="small"
                value={dateFilter}
                onChange={(e) => setDateFilter(e.target.value)}
                sx={{ minWidth: 120 }}
              >
                <MenuItem value="7days">7 ngày</MenuItem>
                <MenuItem value="30days">30 ngày</MenuItem>
                <MenuItem value="90days">90 ngày</MenuItem>
              </TextField>
            </Box>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Ngày</TableCell>
                    <TableCell align="right">Doanh thu</TableCell>
                    <TableCell align="right">Đơn hàng</TableCell>
                    <TableCell align="right">Trung bình/đơn</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {revenueData.map((row) => (
                    <TableRow key={row.date}>
                      <TableCell>
                        {new Date(row.date).toLocaleDateString("vi-VN")}
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body2" color="success.main" fontWeight="bold">
                          {row.revenue.toLocaleString("vi-VN")}đ
                        </Typography>
                      </TableCell>
                      <TableCell align="right">{row.orders}</TableCell>
                      <TableCell align="right">
                        {Math.round(row.revenue / row.orders).toLocaleString("vi-VN")}đ
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
              Sản phẩm bán chạy
            </Typography>
            {topProducts.map((product, index) => (
              <Box key={index} mb={2}>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                  <Typography variant="body2" fontWeight="bold">
                    {product.name}
                  </Typography>
                  <Chip
                    label={`${product.growth > 0 ? '+' : ''}${product.growth}%`}
                    color={product.growth > 0 ? "success" : product.growth < 0 ? "error" : "default"}
                    size="small"
                  />
                </Box>
                <Box display="flex" justifyContent="space-between" mb={1}>
                  <Typography variant="body2" color="text.secondary">
                    Đã bán: {product.sold}
                  </Typography>
                  <Typography variant="body2" color="success.main">
                    {product.revenue.toLocaleString("vi-VN")}đ
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={(product.sold / 50) * 100}
                  sx={{ height: 6, borderRadius: 3 }}
                />
                {index < topProducts.length - 1 && <Divider sx={{ mt: 2 }} />}
              </Box>
            ))}
          </Card>
        </Grid>
      </Grid>

      {/* Recent Orders */}
      <Card sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
          Đơn hàng gần đây
        </Typography>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Mã đơn</TableCell>
                <TableCell>Khách hàng</TableCell>
                <TableCell align="right">Số tiền</TableCell>
                <TableCell>Trạng thái</TableCell>
                <TableCell>Ngày</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {recentOrders.map((order) => (
                <TableRow key={order.id}>
                  <TableCell>
                    <Typography variant="body2" fontWeight="bold">
                      {order.id}
                    </Typography>
                  </TableCell>
                  <TableCell>{order.customer}</TableCell>
                  <TableCell align="right">
                    <Typography variant="body2" fontWeight="bold">
                      {order.amount.toLocaleString("vi-VN")}đ
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={getStatusText(order.status)}
                      color={getStatusColor(order.status) as any}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    {new Date(order.date).toLocaleDateString("vi-VN")}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Card>

      {/* Quick Actions */}
      <Card sx={{ p: 3 }}>
        <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
          Thao tác nhanh
        </Typography>
        <Box display="flex" gap={2} flexWrap="wrap">
          <Button
            variant="contained"
            onClick={() => navigate("/admin/products")}
            startIcon={<Assessment />}
          >
            Quản lý sản phẩm
          </Button>
          <Button
            variant="outlined"
            onClick={() => navigate("/admin/orders")}
            startIcon={<LocalShipping />}
          >
            Quản lý đơn hàng
          </Button>
          <Button
            variant="outlined"
            onClick={() => navigate("/admin/users")}
            startIcon={<People />}
          >
            Quản lý người dùng
          </Button>
        </Box>
      </Card>
    </Box>
  );
};

export default AdminDashboard;
