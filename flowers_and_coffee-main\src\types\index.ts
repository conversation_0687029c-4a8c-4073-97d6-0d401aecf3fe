// User types
export interface User {
  userId: number;
  fullName: string;
  email: string;
  phone?: string;
  address?: string;
  gender?: string;
  birthDate?: string;
  roleId: number;
  createdAt?: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  token: {
    Token: string;
    RefreshToken: string;
  };
}

export interface RegisterRequest {
  fullName: string;
  email: string;
  password: string;
  phone: string;
  address: string;
  gender: string;
  birthDate: string;
}

export interface UserUpdateRequest {
  fullName?: string;
  phone?: string;
  address?: string;
  gender?: string;
  birthDate?: string;
  oldPassword?: string;
  newPassword?: string;
  confirmPassword?: string;
}

// Product types
export interface Product {
  productId: number;
  name: string;
  description: string;
  price: number;
  imageUrl: string;
  categoryId: number;
  isAvailable: boolean;
  createdAt: string;
}

export interface ProductCreateRequest {
  name: string;
  description: string;
  price: number;
  imageUrl: string;
  categoryId: number;
  isAvailable: boolean;
}

// Category types
export interface Category {
  categoryId: number;
  name: string;
  description: string;
}

// Cart types
export interface CartItem {
  id: number;
  userId: number;
  productId: number;
  quantity: number;
  product?: Product;
}

export interface CartItemCreateRequest {
  productId: number;
  quantity: number;
}

// Order types
export interface Order {
  orderId: number;
  userId: number;
  orderDate: string;
  totalAmount: number;
  discountAmount: number;
  finalAmount: number;
  status: string;
  note: string;
  deliveryAddress: string;
  promotionCode?: string;
}

export interface OrderCreateRequest {
  note: string;
  deliveryAddress: string;
  promotionCode?: string;
  selectedCartItemIds?: number[];
}

export interface OrderCreateResponse {
  orderId: number;
  paymentUrl: string;
}

export interface OrderDetail {
  orderDetailId: number;
  orderId: number;
  productId: number;
  productDetailId?: number;
  quantity: number;
  unitPrice: number;
  product?: Product;
}

// Payment types
export interface Payment {
  paymentId: number;
  orderId: number;
  paymentMethod: string;
  paidAmount: number;
  paymentDate: string;
  status: string;
}

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// Auth state types
export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  loading: boolean;
}

// Cart state types
export interface CartState {
  items: CartItem[];
  totalItems: number;
  totalAmount: number;
  loading: boolean;
}

// Product state types
export interface ProductState {
  products: Product[];
  categories: Category[];
  loading: boolean;
  selectedProduct: Product | null;
}

// Order state types
export interface OrderState {
  orders: Order[];
  currentOrder: Order | null;
  loading: boolean;
}

// Dashboard types for admin
export interface DashboardStats {
  totalOrders: number;
  totalRevenue: number;
  totalProducts: number;
  totalUsers: number;
  pendingOrders: number;
  completedOrders: number;
  todayRevenue: number;
  todayOrders: number;
}

export interface RevenueByDate {
  date: string;
  revenue: number;
  orderCount: number;
  averageOrderValue: number;
}

export interface TopProduct {
  productId: number;
  productName: string;
  imageUrl: string;
  totalSold: number;
  totalRevenue: number;
  price: number;
}

export interface OrderStatus {
  status: string;
  count: number;
  totalAmount: number;
  percentage: number;
}
