[{"ContainingType": "JucieAndFlower.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "JucieAndFlower.Data.Enities.Login.LoginRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.AuthController", "Method": "Logout", "RelativePath": "api/Auth/logout", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.AuthController", "Method": "GetProfile", "RelativePath": "api/Auth/profile", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.AuthController", "Method": "Refresh", "RelativePath": "api/Auth/refresh-token", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "JucieAndFlower.Data.Enities.Login.TokenModel", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.AuthController", "Method": "Register", "RelativePath": "api/Auth/register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "JucieAndFlower.Data.Enities.User.UserRegisterDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.AuthController", "Method": "UpdateProfile", "RelativePath": "api/Auth/update-profile", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "JucieAndFlower.Data.Enities.User.UserUpdateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.CartController", "Method": "GetCart", "RelativePath": "api/Cart", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.CartController", "Method": "AddOrUpdateCartItem", "RelativePath": "api/Cart", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "JucieAndFlower.Data.Enities.Cart.CartItemCreateDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.CartController", "Method": "DeleteItem", "RelativePath": "api/Cart/{productId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "productId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.CategoryController", "Method": "GetAll", "RelativePath": "api/Category", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.CategoryController", "Method": "Create", "RelativePath": "api/Category", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "JucieAndFlower.Data.Enities.Categories.CategoryNoID", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.CategoryController", "Method": "Get", "RelativePath": "api/Category/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.CategoryController", "Method": "Update", "RelativePath": "api/Category/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "dto", "Type": "JucieAndFlower.Data.Enities.Categories.CategoryNoID", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.CategoryController", "Method": "Delete", "RelativePath": "api/Category/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.OrdersController", "Method": "GetOrderById", "RelativePath": "api/Orders", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.OrdersController", "Method": "CreateOrderFromCart", "RelativePath": "api/Orders/from-cart", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "JucieAndFlower.Data.Enities.Order.OrderFromCartDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.OrdersController", "Method": "PaymentReturn", "RelativePath": "api/Orders/payment-return", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.PaymentController", "Method": "GetPayments", "RelativePath": "api/Payment", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.ProductDetailController", "Method": "GetAll", "RelativePath": "api/ProductDetail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.ProductDetailController", "Method": "Create", "RelativePath": "api/ProductDetail", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "JucieAndFlower.Data.Enities.ProductDetails.ProductDetailCreateDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.ProductDetailController", "Method": "GetById", "RelativePath": "api/ProductDetail/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.ProductDetailController", "Method": "Update", "RelativePath": "api/ProductDetail/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "dto", "Type": "JucieAndFlower.Data.Enities.ProductDetails.ProductDetailCreateDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.ProductDetailController", "Method": "Delete", "RelativePath": "api/ProductDetail/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.ProductsController", "Method": "GetAll", "RelativePath": "api/Products", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.ProductsController", "Method": "Create", "RelativePath": "api/Products", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "product", "Type": "JucieAndFlower.Data.Enities.Product.ProductCreateUpdateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.ProductsController", "Method": "GetById", "RelativePath": "api/Products/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.ProductsController", "Method": "Update", "RelativePath": "api/Products/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "product", "Type": "JucieAndFlower.Data.Enities.Product.ProductCreateUpdateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.ProductsController", "Method": "Delete", "RelativePath": "api/Products/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.PromotionController", "Method": "GetAll", "RelativePath": "api/Promotion", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.PromotionController", "Method": "Create", "RelativePath": "api/Promotion", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "JucieAndFlower.Data.Enities.Promotion.PromotionCreateUpdateDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.PromotionController", "Method": "Get", "RelativePath": "api/Promotion/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.PromotionController", "Method": "Update", "RelativePath": "api/Promotion/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "dto", "Type": "JucieAndFlower.Data.Enities.Promotion.PromotionCreateUpdateDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.PromotionController", "Method": "Delete", "RelativePath": "api/Promotion/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.WorkshopController", "Method": "GetAll", "RelativePath": "api/Workshop", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.WorkshopController", "Method": "Create", "RelativePath": "api/Workshop", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "JucieAndFlower.Data.Enities.Worshop.WorkshopDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.WorkshopController", "Method": "Get", "RelativePath": "api/Workshop/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.WorkshopController", "Method": "Update", "RelativePath": "api/Workshop/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "dto", "Type": "JucieAndFlower.Data.Enities.Worshop.WorkshopDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.WorkshopController", "Method": "Delete", "RelativePath": "api/Workshop/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.WorkshopTicketController", "Method": "GetAll", "RelativePath": "api/WorkshopTicket", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.WorkshopTicketController", "Method": "Create", "RelativePath": "api/WorkshopTicket", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "JucieAndFlower.Data.Enities.Worshop.WorkshopTicketDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.WorkshopTicketController", "Method": "Get", "RelativePath": "api/WorkshopTicket/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.WorkshopTicketController", "Method": "Update", "RelativePath": "api/WorkshopTicket/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "dto", "Type": "JucieAndFlower.Data.Enities.Worshop.WorkshopTicketDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "JucieAndFlower.Controllers.WorkshopTicketController", "Method": "Delete", "RelativePath": "api/WorkshopTicket/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}]