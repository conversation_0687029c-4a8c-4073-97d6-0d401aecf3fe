import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  TextField,
  Button,
  Alert,
  Stack,
  Container
} from '@mui/material';
import { useAppDispatch } from '../stores/hooks';
import { loginStart, loginSuccess, loginFailure } from '../stores/reducers/Authentication';
import { authAPI } from '../services/api';
import { toast } from 'react-toastify';
import { useNavigate } from 'react-router-dom';
import AuthDebug from '../components/AuthDebug';

const TestAdminPage = () => {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  const handleLogin = async () => {
    if (!email || !password) {
      setError('Vui lòng nhập email và password');
      return;
    }

    try {
      setLoading(true);
      setError('');
      dispatch(loginStart());

      console.log('Attempting login with:', { email, password: '***' });

      const response = await authAPI.login(email, password);
      console.log('Login response:', response);

      if (response && response.token) {
        const tokenValue = response.token.Token || response.token;
        console.log('Token extracted:', tokenValue);

        if (tokenValue) {
          // Save token first
          localStorage.setItem('token', tokenValue);
          console.log('Token saved to localStorage');

          // Get user profile after login
          const userProfile = await authAPI.getProfile();
          console.log('User profile:', userProfile);

          dispatch(loginSuccess({
            user: userProfile,
            token: tokenValue,
          }));

          // Redirect based on user role
          if (userProfile.roleId === 4) {
            // Admin role - redirect to admin dashboard
            toast.success("Chào mừng Admin!");
            navigate("/admin/products");
          } else {
            // Regular user - redirect to home
            toast.success("Đăng nhập thành công!");
            navigate("/");
          }
        } else {
          throw new Error('No token found in response');
        }
      }
    } catch (error: any) {
      console.error('Login error:', error);
      dispatch(loginFailure());
      setError(`Đăng nhập thất bại: ${error.message}`);
      toast.error("Đăng nhập thất bại!");
    } finally {
      setLoading(false);
    }
  };

  const testDirectAccess = () => {
    navigate('/admin/products');
  };

  return (
    <Container maxWidth="md" sx={{ mt: 4 }}>
      <Typography variant="h4" sx={{ mb: 4, textAlign: 'center' }}>
        🧪 Test Admin Login
      </Typography>

      <AuthDebug />

      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 3 }}>
            Admin Login Test
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          <Stack spacing={3}>
            <TextField
              label="Email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              fullWidth
              helperText="Default: <EMAIL>"
            />

            <TextField
              label="Password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              fullWidth
              helperText="Enter the admin password"
            />

            <Stack direction="row" spacing={2}>
              <Button
                variant="contained"
                onClick={handleLogin}
                disabled={loading}
                sx={{ flex: 1 }}
              >
                {loading ? 'Đang đăng nhập...' : 'Đăng nhập Admin'}
              </Button>

              <Button
                variant="outlined"
                onClick={testDirectAccess}
                sx={{ flex: 1 }}
              >
                Test Direct Access
              </Button>
            </Stack>
          </Stack>
        </CardContent>
      </Card>

      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            📝 Instructions
          </Typography>
          <Typography variant="body2" sx={{ mb: 2 }}>
            1. Nhập password cho admin user (<EMAIL>)
          </Typography>
          <Typography variant="body2" sx={{ mb: 2 }}>
            2. Click "Đăng nhập Admin" để test login flow
          </Typography>
          <Typography variant="body2" sx={{ mb: 2 }}>
            3. Click "Test Direct Access" để test trực tiếp trang admin
          </Typography>
          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
            Kiểm tra console và Auth Debug Info để xem chi tiết
          </Typography>
        </CardContent>
      </Card>
    </Container>
  );
};

export default TestAdminPage;
