import React, { useState, useEffect } from "react";
import {
  Con<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>ton,
  Alert,
  Card,
  CardContent,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Avatar,
} from "@mui/material";
import {
  Add,
  Edit,
  Delete,
  Visibility,
  VisibilityOff,
  Close
} from "@mui/icons-material";
import { useAppSelector } from "../../../stores/hooks";
import { productsAPI, categoriesAPI } from "../../../services/api";
import { Product, Category } from "../../../types";
import { toast } from "react-toastify";
import { useNavigate } from "react-router-dom";

const SimpleAdminProducts = () => {
  const { user, isAuthenticated } = useAppSelector((state) => state.Authentication);
  const navigate = useNavigate();

  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);

  // Dialog states
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogMode, setDialogMode] = useState<'add' | 'edit'>('add');
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);

  // Form states
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: '',
    categoryId: '',
    imageUrl: '',
    isActive: true
  });

  useEffect(() => {
    console.log('SimpleAdminProducts - Auth state:', {
      isAuthenticated,
      user,
      userRoleId: user?.roleId,
      hasCorrectRole: user?.roleId === 4
    });

    if (!isAuthenticated || user?.roleId !== 4) {
      console.log('SimpleAdminProducts - Access denied, redirecting to home');
      navigate("/");
      return;
    }
    
    console.log('SimpleAdminProducts - Access granted, loading data');
    loadProducts();
    loadCategories();
  }, [isAuthenticated, user, navigate]);

  const loadProducts = async () => {
    try {
      setLoading(true);
      console.log('Loading products for admin...');
      const data = await productsAPI.getAll();
      console.log('Admin products loaded:', data);

      if (Array.isArray(data)) {
        setProducts(data);
      } else {
        console.error('Products data is not an array:', data);
        setProducts([]);
        toast.error("Dữ liệu sản phẩm không hợp lệ");
      }
    } catch (error: any) {
      console.error('Error loading products for admin:', error);
      toast.error(`Không thể tải danh sách sản phẩm: ${error.message}`);
      setProducts([]);
    } finally {
      setLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      console.log('Loading categories for admin...');
      const data = await categoriesAPI.getAll();
      console.log('Admin categories loaded:', data);

      if (Array.isArray(data)) {
        setCategories(data);
      } else {
        console.error('Categories data is not an array:', data);
        setCategories([]);
        toast.error("Dữ liệu danh mục không hợp lệ");
      }
    } catch (error: any) {
      console.error('Error loading categories for admin:', error);
      toast.error(`Không thể tải danh sách danh mục: ${error.message}`);
      setCategories([]);
    }
  };

  // Dialog handlers
  const handleOpenAddDialog = () => {
    setDialogMode('add');
    setSelectedProduct(null);
    setFormData({
      name: '',
      description: '',
      price: '',
      categoryId: '',
      imageUrl: '',
      isActive: true
    });
    setOpenDialog(true);
  };

  const handleOpenEditDialog = (product: Product) => {
    setDialogMode('edit');
    setSelectedProduct(product);
    setFormData({
      name: product.name,
      description: product.description,
      price: product.price.toString(),
      categoryId: product.categoryId.toString(),
      imageUrl: product.imageUrl || '',
      isActive: product.isActive
    });
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedProduct(null);
    setFormData({
      name: '',
      description: '',
      price: '',
      categoryId: '',
      imageUrl: '',
      isActive: true
    });
  };

  const handleFormChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);

      const productData = {
        name: formData.name,
        description: formData.description,
        price: parseFloat(formData.price),
        categoryId: parseInt(formData.categoryId),
        imageUrl: formData.imageUrl,
        isActive: formData.isActive
      };

      if (dialogMode === 'add') {
        await productsAPI.create(productData);
        toast.success('Thêm sản phẩm thành công!');
      } else if (selectedProduct) {
        await productsAPI.update(selectedProduct.productId, productData);
        toast.success('Cập nhật sản phẩm thành công!');
      }

      handleCloseDialog();
      loadProducts();
    } catch (error: any) {
      console.error('Error saving product:', error);
      toast.error(`Lỗi: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (productId: number) => {
    if (!window.confirm('Bạn có chắc chắn muốn xóa sản phẩm này?')) {
      return;
    }

    try {
      setLoading(true);
      await productsAPI.delete(productId);
      toast.success('Xóa sản phẩm thành công!');
      loadProducts();
    } catch (error: any) {
      console.error('Error deleting product:', error);
      toast.error(`Lỗi xóa sản phẩm: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleToggleActive = async (product: Product) => {
    try {
      setLoading(true);
      await productsAPI.update(product.productId, {
        ...product,
        isActive: !product.isActive
      });
      toast.success(`${!product.isActive ? 'Kích hoạt' : 'Vô hiệu hóa'} sản phẩm thành công!`);
      loadProducts();
    } catch (error: any) {
      console.error('Error toggling product status:', error);
      toast.error(`Lỗi: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  console.log('SimpleAdminProducts render - Auth state:', {
    isAuthenticated,
    user,
    userRoleId: user?.roleId,
    hasCorrectRole: user?.roleId === 4
  });

  if (!isAuthenticated || user?.roleId !== 4) {
    console.log('SimpleAdminProducts render - Showing access denied message');
    return (
      <Container maxWidth="md" sx={{ mt: 4 }}>
        <Alert severity="error">
          Bạn không có quyền truy cập trang này
          <br />
          Debug: isAuthenticated={String(isAuthenticated)}, userRoleId={user?.roleId}
        </Alert>
      </Container>
    );
  }

  console.log('SimpleAdminProducts render - Showing admin interface');

  return (
    <Container maxWidth="lg" sx={{ mt: 4 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" sx={{ fontWeight: 700, color: '#1a1a2e', mb: 1 }}>
          📦 Quản lý sản phẩm
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Quản lý toàn bộ sản phẩm trong hệ thống
        </Typography>
      </Box>

      {/* Loading */}
      {loading && (
        <Box display="flex" justifyContent="center" my={4}>
          <CircularProgress />
        </Box>
      )}

      {/* Controls */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h6">
              Sản phẩm ({products.length})
            </Typography>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={handleOpenAddDialog}
              sx={{
                bgcolor: '#4caf50',
                '&:hover': { bgcolor: '#45a049' }
              }}
            >
              Thêm sản phẩm
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Product Table */}
      <Card>
        <CardContent>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow sx={{ bgcolor: '#f5f5f5' }}>
                  <TableCell>Hình ảnh</TableCell>
                  <TableCell>Tên sản phẩm</TableCell>
                  <TableCell>Mô tả</TableCell>
                  <TableCell>Giá</TableCell>
                  <TableCell>Danh mục</TableCell>
                  <TableCell>Trạng thái</TableCell>
                  <TableCell align="center">Thao tác</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {products.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} align="center">
                      <Typography variant="body1" color="text.secondary">
                        Chưa có sản phẩm nào
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  products.map((product) => {
                    const category = categories.find(c => c.categoryId === product.categoryId);
                    return (
                      <TableRow key={product.productId}>
                        <TableCell>
                          <Avatar
                            src={product.imageUrl}
                            alt={product.name}
                            sx={{ width: 50, height: 50 }}
                          >
                            {product.name.charAt(0)}
                          </Avatar>
                        </TableCell>
                        <TableCell>
                          <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                            {product.name}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" color="text.secondary">
                            {product.description.length > 50
                              ? `${product.description.substring(0, 50)}...`
                              : product.description}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" sx={{ fontWeight: 600, color: '#4caf50' }}>
                            {product.price.toLocaleString()} VND
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={category?.name || 'N/A'}
                            size="small"
                            sx={{ bgcolor: '#e3f2fd', color: '#1976d2' }}
                          />
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={product.isActive ? 'Hoạt động' : 'Vô hiệu'}
                            size="small"
                            color={product.isActive ? 'success' : 'error'}
                          />
                        </TableCell>
                        <TableCell align="center">
                          <Box display="flex" gap={1} justifyContent="center">
                            <IconButton
                              size="small"
                              onClick={() => handleToggleActive(product)}
                              sx={{ color: product.isActive ? '#ff9800' : '#4caf50' }}
                            >
                              {product.isActive ? <VisibilityOff /> : <Visibility />}
                            </IconButton>
                            <IconButton
                              size="small"
                              onClick={() => handleOpenEditDialog(product)}
                              sx={{ color: '#2196f3' }}
                            >
                              <Edit />
                            </IconButton>
                            <IconButton
                              size="small"
                              onClick={() => handleDelete(product.productId)}
                              sx={{ color: '#f44336' }}
                            >
                              <Delete />
                            </IconButton>
                          </Box>
                        </TableCell>
                      </TableRow>
                    );
                  })
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Add/Edit Dialog */}
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h6">
              {dialogMode === 'add' ? '➕ Thêm sản phẩm mới' : '✏️ Chỉnh sửa sản phẩm'}
            </Typography>
            <IconButton onClick={handleCloseDialog}>
              <Close />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <TextField
              fullWidth
              label="Tên sản phẩm"
              value={formData.name}
              onChange={(e) => handleFormChange('name', e.target.value)}
              sx={{ mb: 2 }}
              required
            />

            <TextField
              fullWidth
              label="Mô tả"
              value={formData.description}
              onChange={(e) => handleFormChange('description', e.target.value)}
              multiline
              rows={3}
              sx={{ mb: 2 }}
              required
            />

            <TextField
              fullWidth
              label="Giá (VND)"
              type="number"
              value={formData.price}
              onChange={(e) => handleFormChange('price', e.target.value)}
              sx={{ mb: 2 }}
              required
            />

            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Danh mục</InputLabel>
              <Select
                value={formData.categoryId}
                onChange={(e) => handleFormChange('categoryId', e.target.value)}
                label="Danh mục"
                required
              >
                {categories.map((category) => (
                  <MenuItem key={category.categoryId} value={category.categoryId.toString()}>
                    {category.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <TextField
              fullWidth
              label="URL hình ảnh"
              value={formData.imageUrl}
              onChange={(e) => handleFormChange('imageUrl', e.target.value)}
              sx={{ mb: 2 }}
              placeholder="https://example.com/image.jpg"
            />

            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Trạng thái</InputLabel>
              <Select
                value={formData.isActive}
                onChange={(e) => handleFormChange('isActive', e.target.value)}
                label="Trạng thái"
              >
                <MenuItem value={true}>Hoạt động</MenuItem>
                <MenuItem value={false}>Vô hiệu hóa</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </DialogContent>
        <DialogActions sx={{ p: 3 }}>
          <Button onClick={handleCloseDialog} color="inherit">
            Hủy
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={!formData.name || !formData.description || !formData.price || !formData.categoryId}
            sx={{
              bgcolor: '#4caf50',
              '&:hover': { bgcolor: '#45a049' }
            }}
          >
            {dialogMode === 'add' ? 'Thêm sản phẩm' : 'Cập nhật'}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default SimpleAdminProducts;
