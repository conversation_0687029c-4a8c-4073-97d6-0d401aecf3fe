import React, { useState, useEffect } from "react";
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Alert,
  CircularProgress,
  Chip,
  Avatar,
} from "@mui/material";
import {
  Add,
  Edit,
  Delete,
  Visibility,
  VisibilityOff,
} from "@mui/icons-material";
import { useAppSelector } from "../../../stores/hooks";
import { productsAPI, categoriesAPI } from "../../../services/api";
import { Product, Category } from "../../../types";
import { toast } from "react-toastify";
import { useNavigate } from "react-router-dom";

const AdminProducts = () => {
  const { user, isAuthenticated } = useAppSelector((state) => state.Authentication);
  const navigate = useNavigate();

  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [error, setError] = useState("");

  const [formData, setFormData] = useState({
    name: "",
    description: "",
    price: 0,
    imageUrl: "",
    categoryId: 0,
    isAvailable: true,
  });

  useEffect(() => {
    if (!isAuthenticated || user?.roleId !== 4) {
      navigate("/");
      return;
    }
    loadProducts();
    loadCategories();
  }, [isAuthenticated, user, navigate]);

  const loadProducts = async () => {
    try {
      setLoading(true);
      console.log('Loading products for admin...');
      const data = await productsAPI.getAll();
      console.log('Admin products loaded:', data);

      if (Array.isArray(data)) {
        setProducts(data);
      } else {
        console.error('Products data is not an array:', data);
        setProducts([]);
        toast.error("Dữ liệu sản phẩm không hợp lệ");
      }
    } catch (error: any) {
      console.error('Error loading products for admin:', error);
      toast.error(`Không thể tải danh sách sản phẩm: ${error.message}`);
      setProducts([]);
    } finally {
      setLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      console.log('Loading categories for admin...');
      const data = await categoriesAPI.getAll();
      console.log('Admin categories loaded:', data);

      if (Array.isArray(data)) {
        setCategories(data);
      } else {
        console.error('Categories data is not an array:', data);
        setCategories([]);
        toast.error("Dữ liệu danh mục không hợp lệ");
      }
    } catch (error: any) {
      console.error('Error loading categories for admin:', error);
      toast.error(`Không thể tải danh sách danh mục: ${error.message}`);
      setCategories([]);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleOpenDialog = (product?: Product) => {
    if (product) {
      setEditingProduct(product);
      setFormData({
        name: product.name,
        description: product.description,
        price: product.price,
        imageUrl: product.imageUrl,
        categoryId: product.categoryId,
        isAvailable: product.isAvailable,
      });
    } else {
      setEditingProduct(null);
      setFormData({
        name: "",
        description: "",
        price: 0,
        imageUrl: "",
        categoryId: 0,
        isAvailable: true,
      });
    }
    setDialogOpen(true);
    setError("");
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingProduct(null);
    setError("");
  };

  const handleSubmit = async () => {
    setError("");

    if (!formData.name || !formData.description || formData.price <= 0 || !formData.imageUrl || formData.categoryId === 0) {
      setError("Vui lòng điền đầy đủ thông tin");
      return;
    }

    try {
      setLoading(true);

      if (editingProduct) {
        console.log('Updating product:', editingProduct.productId, formData);
        await productsAPI.update(editingProduct.productId, formData);
        toast.success("Cập nhật sản phẩm thành công!");
      } else {
        console.log('Creating product:', formData);
        await productsAPI.create(formData);
        toast.success("Thêm sản phẩm thành công!");
      }

      handleCloseDialog();
      loadProducts();
    } catch (error: any) {
      console.error('Error saving product:', error);
      setError(`Có lỗi xảy ra khi lưu sản phẩm: ${error.message}`);
      toast.error(`Lưu sản phẩm thất bại: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (productId: number) => {
    if (!window.confirm("Bạn có chắc chắn muốn xóa sản phẩm này?")) {
      return;
    }

    try {
      await productsAPI.delete(productId);
      toast.success("Xóa sản phẩm thành công!");
      loadProducts();
    } catch (error) {
      toast.error("Không thể xóa sản phẩm");
    }
  };

  const getCategoryName = (categoryId: number) => {
    const category = categories.find(c => c.categoryId === categoryId);
    return category?.name || "Không xác định";
  };

  if (!isAuthenticated || user?.roleId !== 4) {
    return (
      <Container maxWidth="md" sx={{ mt: 4 }}>
        <Alert severity="error">
          Bạn không có quyền truy cập trang này
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
        <Typography variant="h4" component="h1">
          Quản lý sản phẩm
        </Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => handleOpenDialog()}
        >
          Thêm sản phẩm
        </Button>
      </Box>

      {loading && (
        <Box display="flex" justifyContent="center" my={4}>
          <CircularProgress />
        </Box>
      )}

      <Paper>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Hình ảnh</TableCell>
                <TableCell>Tên sản phẩm</TableCell>
                <TableCell>Mô tả</TableCell>
                <TableCell>Giá</TableCell>
                <TableCell>Danh mục</TableCell>
                <TableCell>Trạng thái</TableCell>
                <TableCell align="center">Thao tác</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {products.map((product) => (
                <TableRow key={product.productId}>
                  <TableCell>
                    <Avatar
                      src={product.imageUrl}
                      alt={product.name}
                      variant="rounded"
                      sx={{ width: 60, height: 60 }}
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="subtitle2">
                      {product.name}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" noWrap sx={{ maxWidth: 200 }}>
                      {product.description}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {product.price.toLocaleString("vi-VN")}đ
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={getCategoryName(product.categoryId)}
                      size="small"
                      variant="outlined"
                    />
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={product.isAvailable ? "Có sẵn" : "Hết hàng"}
                      color={product.isAvailable ? "success" : "error"}
                      size="small"
                    />
                  </TableCell>
                  <TableCell align="center">
                    <IconButton
                      size="small"
                      onClick={() => handleOpenDialog(product)}
                      color="primary"
                    >
                      <Edit />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => handleDelete(product.productId)}
                      color="error"
                    >
                      <Delete />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      {/* Add/Edit Dialog */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingProduct ? "Chỉnh sửa sản phẩm" : "Thêm sản phẩm mới"}
        </DialogTitle>
        <DialogContent>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <TextField
            fullWidth
            label="Tên sản phẩm"
            value={formData.name}
            onChange={(e) => handleInputChange("name", e.target.value)}
            margin="normal"
            required
          />

          <TextField
            fullWidth
            label="Mô tả"
            value={formData.description}
            onChange={(e) => handleInputChange("description", e.target.value)}
            margin="normal"
            multiline
            rows={3}
            required
          />

          <TextField
            fullWidth
            label="Giá (VNĐ)"
            type="number"
            value={formData.price}
            onChange={(e) => handleInputChange("price", Number(e.target.value))}
            margin="normal"
            required
          />

          <TextField
            fullWidth
            label="URL hình ảnh"
            value={formData.imageUrl}
            onChange={(e) => handleInputChange("imageUrl", e.target.value)}
            margin="normal"
            required
          />

          <FormControl fullWidth margin="normal" required>
            <InputLabel>Danh mục</InputLabel>
            <Select
              value={formData.categoryId}
              label="Danh mục"
              onChange={(e) => handleInputChange("categoryId", e.target.value)}
            >
              {categories.map((category) => (
                <MenuItem key={category.categoryId} value={category.categoryId}>
                  {category.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControlLabel
            control={
              <Switch
                checked={formData.isAvailable}
                onChange={(e) => handleInputChange("isAvailable", e.target.checked)}
              />
            }
            label="Có sẵn"
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Hủy</Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={loading}
          >
            {loading ? <CircularProgress size={20} /> : "Lưu"}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default AdminProducts;
