import { useState } from "react";
import { styled } from "@mui/material/styles";
import {
  Container,
  TextField,
  Button,
  Checkbox,
  FormControlLabel,
  Typography,
  Divider,
  Box,
  FormControl,
  OutlinedInput,
  InputLabel,
  IconButton,
  InputAdornment,
  Alert,
  CircularProgress,
  Select,
  MenuItem,
} from "@mui/material";
import FacebookIcon from "@mui/icons-material/Facebook";
import GoogleIcon from "@mui/icons-material/Google";
import Visibility from "@mui/icons-material/Visibility";
import VisibilityOff from "@mui/icons-material/VisibilityOff";
import { useNavigate, Link } from "react-router-dom";
import { authAPI } from "../../services/api";
import { toast } from "react-toastify";



const Regiter = () => {
  const navigate = useNavigate();

  const [showPassword, setShowPassword] = useState(false);
  const [showConfimPassword, setShowConfimPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  // Form data
  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    phone: "",
    password: "",
    confirmPassword: "",
    address: "",
    gender: "",
    birthDate: "",
  });



  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    setError("");
  };

  const handleClickShowPassword = () => setShowPassword((show) => !show);

  const handleMouseDownPassword = (
    event: React.MouseEvent<HTMLButtonElement>
  ) => {
    event.preventDefault();
  };

  const handleMouseUpPassword = (
    event: React.MouseEvent<HTMLButtonElement>
  ) => {
    event.preventDefault();
  };

  const handleClickShowConfimPassword = () =>
    setShowConfimPassword((show) => !show);

  const handleMouseDownConfimPassword = (
    event: React.MouseEvent<HTMLButtonElement>
  ) => {
    event.preventDefault();
  };

  const handleMouseUpConfimPassword = (
    event: React.MouseEvent<HTMLButtonElement>
  ) => {
    event.preventDefault();
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    // Validation
    if (!formData.fullName.trim()) {
      setError("Vui lòng nhập họ và tên");
      return;
    }

    if (!formData.email.trim()) {
      setError("Vui lòng nhập email");
      return;
    }

    if (!formData.password) {
      setError("Vui lòng nhập mật khẩu");
      return;
    }

    if (formData.password.length < 6) {
      setError("Mật khẩu phải có ít nhất 6 ký tự");
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      setError("Mật khẩu xác nhận không khớp");
      return;
    }

    if (!formData.phone.trim()) {
      setError("Vui lòng nhập số điện thoại");
      return;
    }

    if (!formData.address.trim()) {
      setError("Vui lòng nhập địa chỉ");
      return;
    }

    if (!formData.gender.trim()) {
      setError("Vui lòng chọn giới tính");
      return;
    }

    if (!formData.birthDate.trim()) {
      setError("Vui lòng chọn ngày sinh");
      return;
    }

    try {
      setLoading(true);

      const registerData = {
        fullName: formData.fullName,
        email: formData.email,
        password: formData.password,
        phone: formData.phone,
        address: formData.address,
        gender: formData.gender,
        birthDate: formData.birthDate,
      };

      await authAPI.register(registerData);

      toast.success("Đăng ký thành công! Vui lòng đăng nhập.");
      navigate("/auth/login");
    } catch (error: any) {
      let errorMessage = "Đăng ký thất bại";

      // Try to extract error message from response
      if (error.message) {
        errorMessage = error.message;
      }

      // Handle specific error cases
      if (errorMessage.includes("Email already exists")) {
        errorMessage = "Email hoặc số điện thoại đã được sử dụng";
      } else if (errorMessage.includes("Registration failed")) {
        errorMessage = "Đăng ký thất bại. Vui lòng kiểm tra lại thông tin";
      }

      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };
  return (
    <Container maxWidth="xs" sx={{ textAlign: "center", my: 10 }}>
      <Box
        component="form"
        onSubmit={handleSubmit}
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
        }}
      >
        <Typography
          variant="h4"
          component="h1"
          fontWeight="bold"
          sx={{ mb: 2 }}
        >
          ĐĂNG KÝ
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 2, width: "100%" }}>
            {error}
          </Alert>
        )}
        <TextField
          fullWidth
          label="Họ và tên"
          variant="outlined"
          margin="normal"
          size="small"
          value={formData.fullName}
          onChange={(e) => handleInputChange("fullName", e.target.value)}
          required
          sx={{
            mt: 3,
            "& .MuiOutlinedInput-root": {
              borderRadius: "20px",
            },
          }}
        />

        <TextField
          fullWidth
          label="Email của bạn"
          type="email"
          variant="outlined"
          margin="normal"
          size="small"
          value={formData.email}
          onChange={(e) => handleInputChange("email", e.target.value)}
          required
          sx={{
            mt: 2,
            "& .MuiOutlinedInput-root": {
              borderRadius: "20px",
            },
          }}
        />
        <FormControl
          sx={{
            width: "100%",
            "& .MuiOutlinedInput-root": {
              borderRadius: "20px",
            },
            mt: 1,
          }}
          variant="outlined"
          size="small"
        >
          <InputLabel htmlFor="outlined-adornment-password">
            Mật khẩu
          </InputLabel>
          <OutlinedInput
            id="outlined-adornment-password"
            type={showPassword ? "text" : "password"}
            value={formData.password}
            onChange={(e) => handleInputChange("password", e.target.value)}
            required
            endAdornment={
              <InputAdornment position="end">
                <IconButton
                  aria-label={
                    showPassword ? "hide the password" : "display the password"
                  }
                  onClick={handleClickShowPassword}
                  onMouseDown={handleMouseDownPassword}
                  onMouseUp={handleMouseUpPassword}
                  edge="end"
                >
                  {showPassword ? <VisibilityOff /> : <Visibility />}
                </IconButton>
              </InputAdornment>
            }
            label="Mật khẩu"
          />
        </FormControl>

        <FormControl
          sx={{
            width: "100%",
            mt: 2,
            "& .MuiOutlinedInput-root": {
              borderRadius: "20px",
            },
          }}
          variant="outlined"
          size="small"
        >
          <InputLabel htmlFor="outlined-adornment-password">
            Xác nhận mật khẩu
          </InputLabel>
          <OutlinedInput
            id="outlined-adornment-confirm-password"
            type={showConfimPassword ? "text" : "password"}
            value={formData.confirmPassword}
            onChange={(e) => handleInputChange("confirmPassword", e.target.value)}
            required
            endAdornment={
              <InputAdornment position="end">
                <IconButton
                  aria-label={
                    showConfimPassword
                      ? "hide the password"
                      : "display the password"
                  }
                  onClick={handleClickShowConfimPassword}
                  onMouseDown={handleMouseDownConfimPassword}
                  onMouseUp={handleMouseUpConfimPassword}
                  edge="end"
                >
                  {showConfimPassword ? <VisibilityOff /> : <Visibility />}
                </IconButton>
              </InputAdornment>
            }
            label="Xác nhận mật khẩu"
          />
        </FormControl>

        {/* Phone Number Field */}
        <TextField
          fullWidth
          label="Số điện thoại"
          variant="outlined"
          margin="normal"
          size="small"
          value={formData.phone}
          onChange={(e) => handleInputChange("phone", e.target.value)}
          required
          sx={{
            mt: 2,
            "& .MuiOutlinedInput-root": {
              borderRadius: "20px",
            },
          }}
        />

        {/* Address Field */}
        <TextField
          fullWidth
          label="Địa chỉ"
          variant="outlined"
          margin="normal"
          size="small"
          value={formData.address}
          onChange={(e) => handleInputChange("address", e.target.value)}
          required
          sx={{
            mt: 2,
            "& .MuiOutlinedInput-root": {
              borderRadius: "20px",
            },
          }}
        />

        {/* Gender Field */}
        <FormControl
          fullWidth
          size="small"
          sx={{
            mt: 2,
            "& .MuiOutlinedInput-root": {
              borderRadius: "20px",
            },
          }}
        >
          <InputLabel>Giới tính</InputLabel>
          <Select
            value={formData.gender}
            label="Giới tính"
            onChange={(e) => handleInputChange("gender", e.target.value)}
            required
          >
            <MenuItem value="Nam">Nam</MenuItem>
            <MenuItem value="Nữ">Nữ</MenuItem>
            <MenuItem value="Khác">Khác</MenuItem>
          </Select>
        </FormControl>

        {/* Birth Date Field */}
        <TextField
          fullWidth
          label="Ngày sinh"
          type="date"
          variant="outlined"
          margin="normal"
          size="small"
          value={formData.birthDate}
          onChange={(e) => handleInputChange("birthDate", e.target.value)}
          required
          InputLabelProps={{
            shrink: true,
          }}
          sx={{
            mt: 2,
            "& .MuiOutlinedInput-root": {
              borderRadius: "20px",
            },
          }}
        />

        <Button
          type="submit"
          fullWidth
          variant="contained"
          size="small"
          disabled={loading}
          sx={{
            mt: 2,
            mb: 2,
            py: 1,
            backgroundColor: "#D9D9D9",
            color: "black",
            borderRadius: "20px",
            "&:hover": {
              backgroundColor: "#d5d5d5",
            },
          }}
        >
          {loading ? <CircularProgress size={20} color="inherit" /> : "ĐĂNG KÝ"}
        </Button>
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            width: "100%",
          }}
        >
          <FormControlLabel
            control={<Checkbox value="remember" color="primary" size="small" />}
            label={<Typography variant="body2">Ghi nhớ mật khẩu</Typography>}
          />
          <Typography
            variant="body2"
            component="a"
            href="#"
            sx={{ textDecoration: "none" }}
          >
            Quên mật khẩu?
          </Typography>
        </Box>
        <Box sx={{ display: "flex", alignItems: "center", my: 1 }}>
          <Divider sx={{ flexGrow: 1 }} />
          <Typography variant="body2" sx={{ mx: 2 }}>
            HOẶC
          </Typography>
          <Divider sx={{ flexGrow: 1 }} />
        </Box>

        <Box sx={{ display: "flex", justifyContent: "center", gap: 3, mb: 1 }}>
          <IconButton
            sx={{ border: "1px solid rgb(0, 0, 0)", borderRadius: "50%" }}
          >
            <FacebookIcon />
          </IconButton>
          <IconButton
            sx={{ border: "1px solid rgb(0, 0, 0)", borderRadius: "50%" }}
          >
            <GoogleIcon />
          </IconButton>
        </Box>

        <Box sx={{ textAlign: "center" }}>
          <Typography variant="body1" component="span" color="text.secondary">
            Bạn đã có tài khoản?{" "}
          </Typography>
          <Typography
            variant="body1"
            component={Link}
            to="/auth/login"
            sx={{
              fontWeight: "bold",
              textDecoration: "none",
              color: "black",
              "&:hover": {
                textDecoration: "underline",
              },
            }}
          >
            Đăng nhập
          </Typography>
        </Box>
      </Box>
    </Container>
  );
};

export default Regiter;
