import React, { useState } from "react";
import {
  Container,
  Paper,
  Typography,
  Box,
  Grid,
  TextField,
  Button,
  Divider,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
} from "@mui/material";
import { Payment, LocalShipping, Receipt } from "@mui/icons-material";
import { useAppSelector, useAppDispatch } from "../../stores/hooks";
import { clearCart } from "../../stores/reducers/Cart";
import { ordersAPI } from "../../services/api";
import { toast } from "react-toastify";
import { useNavigate } from "react-router-dom";

const CheckoutPage = () => {
  const { items, totalAmount } = useAppSelector((state) => state.Cart);
  const { user, isAuthenticated } = useAppSelector((state) => state.Authentication);
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const [orderData, setOrderData] = useState({
    note: "",
    deliveryAddress: user?.address || "",
    promotionCode: "",
  });

  const handleInputChange = (field: string, value: string) => {
    setOrderData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    if (!orderData.deliveryAddress.trim()) {
      setError("Vui lòng nhập địa chỉ giao hàng");
      return;
    }

    if (items.length === 0) {
      setError("Giỏ hàng trống");
      return;
    }

    try {
      setLoading(true);
      console.log('Creating order with data:', orderData);
      const order = await ordersAPI.createFromCart(orderData);
      console.log('Order created successfully:', order);

      // Clear cart after successful order
      dispatch(clearCart());

      toast.success("Đặt hàng thành công! Chuyển hướng đến trang thanh toán...");

      // Handle payment URL from VNPay
      if (order && order.paymentUrl) {
        console.log('Redirecting to payment URL:', order.paymentUrl);
        // Redirect to VNPay payment page
        window.location.href = order.paymentUrl;
      } else if (order && order.orderId) {
        navigate(`/orders/${order.orderId}`);
      } else if (order && order.OrderId) {
        navigate(`/orders/${order.OrderId}`);
      } else {
        // Fallback to orders list if no orderId
        navigate('/orders');
      }
    } catch (error: any) {
      console.error('Error creating order:', error);
      const errorMessage = error.message || "Có lỗi xảy ra khi đặt hàng. Vui lòng thử lại.";
      setError(errorMessage);
      toast.error(`Đặt hàng thất bại: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  if (!isAuthenticated) {
    return (
      <Container maxWidth="md" sx={{ mt: 4 }}>
        <Alert severity="warning">
          Vui lòng đăng nhập để thanh toán
        </Alert>
      </Container>
    );
  }

  if (items.length === 0) {
    return (
      <Container maxWidth="md" sx={{ mt: 4 }}>
        <Alert severity="info">
          Giỏ hàng trống. <Button onClick={() => navigate("/flower")}>Tiếp tục mua sắm</Button>
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" gutterBottom>
        <Payment sx={{ mr: 2, verticalAlign: "middle" }} />
        Thanh toán
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              <LocalShipping sx={{ mr: 1, verticalAlign: "middle" }} />
              Thông tin giao hàng
            </Typography>

            <Box component="form" onSubmit={handleSubmit}>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Họ và tên"
                    value={user?.fullName || ""}
                    disabled
                    variant="filled"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Số điện thoại"
                    value={user?.phone || ""}
                    disabled
                    variant="filled"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Địa chỉ giao hàng"
                    value={orderData.deliveryAddress}
                    onChange={(e) => handleInputChange("deliveryAddress", e.target.value)}
                    required
                    multiline
                    rows={2}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Ghi chú đơn hàng"
                    value={orderData.note}
                    onChange={(e) => handleInputChange("note", e.target.value)}
                    multiline
                    rows={3}
                    placeholder="Ghi chú thêm cho đơn hàng (tùy chọn)"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Mã khuyến mãi"
                    value={orderData.promotionCode}
                    onChange={(e) => handleInputChange("promotionCode", e.target.value)}
                    placeholder="Nhập mã khuyến mãi (nếu có)"
                  />
                </Grid>
              </Grid>
            </Box>
          </Paper>

          <Paper elevation={2} sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              <Receipt sx={{ mr: 1, verticalAlign: "middle" }} />
              Phương thức thanh toán
            </Typography>

            <Card variant="outlined" sx={{ mt: 2 }}>
              <CardContent>
                <Typography variant="body1" gutterBottom>
                  💳 Thanh toán qua VNPay
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Thanh toán an toàn qua cổng VNPay (ATM, Visa, MasterCard)
                </Typography>
              </CardContent>
            </Card>
          </Paper>
        </Grid>

        <Grid item xs={12} md={4}>
          <Paper elevation={2} sx={{ p: 3, position: "sticky", top: 20 }}>
            <Typography variant="h6" gutterBottom>
              Đơn hàng của bạn
            </Typography>

            <List dense>
              {items.map((item) => (
                <ListItem key={item.id} sx={{ px: 0 }}>
                  <ListItemAvatar>
                    <Avatar
                      src={item.product?.imageUrl}
                      alt={item.product?.name}
                      variant="rounded"
                    />
                  </ListItemAvatar>
                  <ListItemText
                    primary={item.product?.name}
                    secondary={`Số lượng: ${item.quantity}`}
                  />
                  <Typography variant="body2">
                    {((item.product?.price || 0) * item.quantity).toLocaleString("vi-VN")}đ
                  </Typography>
                </ListItem>
              ))}
            </List>

            <Divider sx={{ my: 2 }} />

            <Box display="flex" justifyContent="space-between" mb={1}>
              <Typography>Tạm tính:</Typography>
              <Typography>{totalAmount.toLocaleString("vi-VN")}đ</Typography>
            </Box>

            <Box display="flex" justifyContent="space-between" mb={1}>
              <Typography>Phí vận chuyển:</Typography>
              <Typography>Miễn phí</Typography>
            </Box>

            <Box display="flex" justifyContent="space-between" mb={1}>
              <Typography>Giảm giá:</Typography>
              <Typography>0đ</Typography>
            </Box>

            <Divider sx={{ my: 2 }} />

            <Box display="flex" justifyContent="space-between" mb={3}>
              <Typography variant="h6">Tổng cộng:</Typography>
              <Typography variant="h6" color="primary">
                {totalAmount.toLocaleString("vi-VN")}đ
              </Typography>
            </Box>

            <Button
              fullWidth
              variant="contained"
              size="large"
              onClick={handleSubmit}
              disabled={loading}
              sx={{ mb: 2 }}
            >
              {loading ? (
                <CircularProgress size={24} color="inherit" />
              ) : (
                "Đặt hàng"
              )}
            </Button>

            <Button
              fullWidth
              variant="outlined"
              onClick={() => navigate("/cart")}
              disabled={loading}
            >
              Quay lại giỏ hàng
            </Button>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default CheckoutPage;
